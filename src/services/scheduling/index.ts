// Employee Assignment System Services
// Server-first architecture for scheduling domain

import { AssignmentService } from "./AssignmentService";
import { ConflictDetectionService } from "./ConflictDetectionService";
import { EmployeeAvailabilityService } from "./EmployeeAvailabilityService";

export { AssignmentService } from "./AssignmentService";
export type {
  AssignmentWithDetails,
  CreateAssignmentRequest,
  AssignmentFilters,
  AssignmentHistory,
} from "./AssignmentService";

export { ConflictDetectionService } from "./ConflictDetectionService";
export type {
  AssignmentConflict,
  ConflictResolution,
  AvailabilityCheck,
  ConflictSummary,
} from "./ConflictDetectionService";

export { EmployeeAvailabilityService } from "./EmployeeAvailabilityService";
export type {
  AvailabilitySlot,
  EmployeeSchedule,
  AvailabilityFilters,
  WorkloadDistribution,
} from "./EmployeeAvailabilityService";

// Service factory functions for dependency injection
export const createAssignmentService = () => new AssignmentService();
export const createConflictDetectionService = () => new ConflictDetectionService();
export const createEmployeeAvailabilityService = () => new EmployeeAvailabilityService();
