import { NextRequest, NextResponse } from "next/server";
import { logger } from "@/lib/logger/services/LoggerService";
import { DocumentUploadService } from "@/app/[lang]/protected/document/lib/services/DocumentUploadService";

/**
 * Upload document attachments via API route
 * POST /api/documents/upload
 */
export async function POST(request: NextRequest) {
  try {
    // Parse form data
    const formData = await request.formData();
    const files = formData.getAll("files") as File[];
    const documentName = formData.get("name") as string;
    const attachedToType = formData.get("attached_to_type") as string;
    const attachedToId = formData.get("attached_to_id") as string;
    const category = formData.get("category") as string;
    const tagsValue = formData.get("tags") as string;
    let tags: string[] = [];
    if (tagsValue) {
      try {
        tags = JSON.parse(tagsValue);
      } catch (error) {
        // If it's not valid JSON, treat it as a single tag
        tags = [tagsValue];
      }
    }
    const description = formData.get("description") as string;

    // Validate required fields
    if (!files || files.length === 0) {
      return NextResponse.json({ error: "No files provided" }, { status: 400 });
    }

    if (!documentName || !documentName.trim()) {
      return NextResponse.json({ error: "Document name is required" }, { status: 400 });
    }

    if (!attachedToType || !attachedToId) {
      return NextResponse.json(
        { error: "attached_to_type and attached_to_id are required" },
        { status: 400 }
      );
    }

    // Process uploads using DocumentUploadService
    const uploadedAttachments: any[] = [];
    const errors: string[] = [];

    for (const file of files) {
      try {
        const uploadResult = await DocumentUploadService.uploadDocument({
          file,
          documentName: documentName.trim(),
          attachedToType,
          attachedToId,
          category,
          tags,
          description,
          attachmentType: "manual",
        });

        if (uploadResult.success && uploadResult.data) {
          uploadedAttachments.push(uploadResult.data);
          logger.info(`File uploaded successfully: ${file.name}`);
        } else {
          errors.push(`${file.name}: ${uploadResult.message}`);
        }
      } catch (error) {
        logger.error(`Error processing file ${file.name}:`, error as Error);
        errors.push(`${file.name}: Processing failed`);
      }
    }

    // Return results
    if (uploadedAttachments.length === 0) {
      return NextResponse.json(
        {
          error: `All uploads failed: ${errors.join(", ")}`,
          uploadedFiles: [],
          errors,
        },
        { status: 400 }
      );
    }

    const response = {
      message: `Successfully uploaded ${uploadedAttachments.length} files`,
      uploadedFiles: uploadedAttachments,
      errors: errors.length > 0 ? errors : undefined,
    };

    logger.info(
      `Successfully uploaded ${uploadedAttachments.length} files for ${attachedToType}:${attachedToId}`
    );

    return NextResponse.json(response, { status: 201 });
  } catch (error) {
    logger.error("Error in upload API route:", error as Error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
