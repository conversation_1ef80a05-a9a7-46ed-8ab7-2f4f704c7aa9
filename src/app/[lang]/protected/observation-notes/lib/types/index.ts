/**
 * Observation Notes Types
 *
 * Types extracted from the database schema for observation notes domain.
 */

import { Database } from "@/lib/types/database.types";

// Extract types from database schema
export type ObservationNote = Database["public"]["Tables"]["observation_notes"]["Row"];
export type ObservationNoteInsert = Database["public"]["Tables"]["observation_notes"]["Insert"];
export type ObservationNoteUpdate = Database["public"]["Tables"]["observation_notes"]["Update"];

export type ObservationNoteHistory =
  Database["public"]["Tables"]["observation_note_history"]["Row"];
export type ObservationNoteHistoryInsert =
  Database["public"]["Tables"]["observation_note_history"]["Insert"];

export type NoteStatus = Database["public"]["Enums"]["note_status"];

// Extended types for UI components
export interface ObservationNoteWithRelations extends ObservationNote {
  appointment?: {
    id: string;
    title: string;
    appointment_date: string;
    start_time: string;
    end_time: string;
    case_file_id: string;
  };
  case_file?: {
    id: string;
    case_number: string;
    status: string;
  };
  created_by_user?: {
    id: string;
    email: string;
  };
  approved_by_user?: {
    id: string;
    email: string;
  };
}

// Form data types
export interface CreateObservationNoteForm {
  appointment_id: string;
  title: string;
  content: string;
}

export interface EditObservationNoteForm {
  title: string;
  content: string;
  status?: NoteStatus;
}

// List and filter types
export interface ObservationNoteListItem {
  id: string;
  title: string;
  content: string;
  status: NoteStatus;
  created_at: string;
  updated_at: string;
  appointment_title: string;
  appointment_date: string;
  case_number: string;
  created_by_email: string;
}

export interface ObservationNoteFilters {
  status?: NoteStatus;
  appointment_id?: string;
  case_file_id?: string;
  created_by?: string;
  date_from?: string;
  date_to?: string;
}

// Service response types
export interface ObservationNoteServiceResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}
