/**
 * Observation Notes Domain Configuration
 *
 * This file contains the configuration for the observation notes domain,
 * including permissions, routes, and domain metadata.
 */

// Domain identifier (used in permissions, routes, etc.)
export const DOMAIN_ID = "observation-notes";

// Domain display name (used in UI)
export const DOMAIN_NAME = "Observation Notes";

// Base path for all routes in this domain
export const DOMAIN_BASE_PATH = "/protected/observation-notes";

// Domain description (used in UI)
export const DOMAIN_DESCRIPTION = "Observation notes for appointments related to case files";

// Domain permissions
export const DOMAIN_PERMISSIONS = {
  VIEW: `${DOMAIN_ID}:view`,
  CREATE: `${DOMAIN_ID}:create`,
  UPDATE: `${DOMAIN_ID}:update`,
  DELETE: `${DOMAIN_ID}:delete`,
  ADMIN: `${DOMAIN_ID}:admin`,
};

// Route configuration
export const ROUTES = {
  LIST: DOMAIN_BASE_PATH,
  CREATE: `${DOMAIN_BASE_PATH}/new`,
  VIEW: (id: string) => `${DOMAIN_BASE_PATH}/${id}`,
  EDIT: (id: string) => `${DOMAIN_BASE_PATH}/${id}/edit`,
};

// Domain configuration object
const DOMAIN_CONFIG = {
  id: DOMAIN_ID,
  name: DOMAIN_NAME,
  basePath: DOMAIN_BASE_PATH,
  description: DOMAIN_DESCRIPTION,
  permissions: DOMAIN_PERMISSIONS,
  routes: ROUTES,
};

export default DOMAIN_CONFIG;
