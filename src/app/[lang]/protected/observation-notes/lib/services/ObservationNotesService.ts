/**
 * Observation Notes Service
 *
 * Service layer for observation notes CRUD operations.
 */

import { createClient } from "@/lib/supabase/server";
import { logger } from "@/lib/logger/services/LoggerService";
import { auth } from "@/lib/authentication/services/AuthenticationService";
import {
  ObservationNote,
  ObservationNoteInsert,
  ObservationNoteUpdate,
  ObservationNoteWithRelations,
  ObservationNoteListItem,
  ObservationNoteFilters,
  ObservationNoteServiceResponse,
} from "../types";

export class ObservationNotesService {
  /**
   * Create a new observation note
   */
  static async create(
    data: Omit<
      ObservationNoteInsert,
      "id" | "organization_id" | "created_by" | "created_at" | "updated_at"
    >
  ): Promise<ObservationNoteServiceResponse<ObservationNote>> {
    try {
      const supabase = await createClient();
      const currentUser = await auth.getCurrentUser();

      if (!currentUser) {
        return { success: false, error: "User not authenticated" };
      }

      const organizationId = await auth.getCurrentUserOrganizationId();
      if (!organizationId) {
        return { success: false, error: "User organization not found" };
      }

      // Verify the appointment exists and belongs to a case file
      const { data: appointment, error: appointmentError } = await supabase
        .from("appointments")
        .select("id, case_file_id, organization_id")
        .eq("id", data.attached_to_id)
        .eq("organization_id", organizationId)
        .single();

      if (appointmentError || !appointment) {
        return { success: false, error: "Appointment not found" };
      }

      if (!appointment.case_file_id) {
        return {
          success: false,
          error: "Observation notes can only be created for appointments related to case files",
        };
      }

      const noteData: ObservationNoteInsert = {
        ...data,
        organization_id: organizationId,
        created_by: currentUser.id,
        attached_to_type: "appointment",
      };

      const { data: note, error } = await supabase
        .from("observation_notes")
        .insert(noteData)
        .select()
        .single();

      if (error) {
        logger.error(`Error creating observation note: ${error.message}`);
        return { success: false, error: error.message };
      }

      return { success: true, data: note };
    } catch (error) {
      logger.error(`Unexpected error creating observation note: ${error}`);
      return { success: false, error: `Unexpected error: ${error}` };
    }
  }

  /**
   * Get observation note by ID with relations
   */
  static async view(
    id: string
  ): Promise<ObservationNoteServiceResponse<ObservationNoteWithRelations>> {
    try {
      const supabase = await createClient();
      const currentUser = await auth.getCurrentUser();

      if (!currentUser) {
        return { success: false, error: "User not authenticated" };
      }

      const organizationId = await auth.getCurrentUserOrganizationId();
      if (!organizationId) {
        return { success: false, error: "User organization not found" };
      }

      const { data: note, error } = await supabase
        .from("observation_notes")
        .select("*")
        .eq("id", id)
        .eq("organization_id", organizationId)
        .single();

      if (error) {
        logger.error(`Error fetching observation note: ${error.message}`);
        return { success: false, error: error.message };
      }

      if (!note) {
        return { success: false, error: "Observation note not found" };
      }

      // For now, return the basic note without relations
      // TODO: Implement proper joins for appointment and case file data
      return { success: true, data: note as any };
    } catch (error) {
      logger.error(`Unexpected error fetching observation note: ${error}`);
      return { success: false, error: `Unexpected error: ${error}` };
    }
  }

  /**
   * Update observation note
   */
  static async update(
    id: string,
    data: ObservationNoteUpdate
  ): Promise<ObservationNoteServiceResponse<ObservationNote>> {
    try {
      const supabase = await createClient();
      const currentUser = await auth.getCurrentUser();

      if (!currentUser) {
        return { success: false, error: "User not authenticated" };
      }

      const organizationId = await auth.getCurrentUserOrganizationId();
      if (!organizationId) {
        return { success: false, error: "User organization not found" };
      }

      const { data: note, error } = await supabase
        .from("observation_notes")
        .update({
          ...data,
          updated_at: new Date().toISOString(),
        })
        .eq("id", id)
        .eq("organization_id", organizationId)
        .select()
        .single();

      if (error) {
        logger.error(`Error updating observation note: ${error.message}`);
        return { success: false, error: error.message };
      }

      return { success: true, data: note };
    } catch (error) {
      logger.error(`Unexpected error updating observation note: ${error}`);
      return { success: false, error: `Unexpected error: ${error}` };
    }
  }

  /**
   * Delete observation note (soft delete by setting status to rejected)
   */
  static async remove(id: string): Promise<ObservationNoteServiceResponse<boolean>> {
    try {
      const supabase = await createClient();
      const currentUser = await auth.getCurrentUser();

      if (!currentUser) {
        return { success: false, error: "User not authenticated" };
      }

      const organizationId = await auth.getCurrentUserOrganizationId();
      if (!organizationId) {
        return { success: false, error: "User organization not found" };
      }

      const { error } = await supabase
        .from("observation_notes")
        .update({ status: "rejected" })
        .eq("id", id)
        .eq("organization_id", organizationId);

      if (error) {
        logger.error(`Error removing observation note: ${error.message}`);
        return { success: false, error: error.message };
      }

      return { success: true, data: true };
    } catch (error) {
      logger.error(`Unexpected error removing observation note: ${error}`);
      return { success: false, error: `Unexpected error: ${error}` };
    }
  }

  /**
   * List observation notes with filters
   */
  static async list(
    filters: ObservationNoteFilters = {}
  ): Promise<ObservationNoteServiceResponse<ObservationNoteListItem[]>> {
    try {
      const supabase = await createClient();
      const currentUser = await auth.getCurrentUser();

      if (!currentUser) {
        return { success: false, error: "User not authenticated" };
      }

      const organizationId = await auth.getCurrentUserOrganizationId();
      if (!organizationId) {
        return { success: false, error: "User organization not found" };
      }

      let query = supabase
        .from("observation_notes")
        .select("id, title, content, status, created_at, updated_at, attached_to_id")
        .eq("organization_id", organizationId)
        .eq("attached_to_type", "appointment")
        .order("created_at", { ascending: false });

      // Apply filters
      if (filters.status) {
        query = query.eq("status", filters.status);
      }
      if (filters.appointment_id) {
        query = query.eq("attached_to_id", filters.appointment_id);
      }
      if (filters.created_by) {
        query = query.eq("created_by", filters.created_by);
      }

      const { data: notes, error } = await query;

      if (error) {
        logger.error(`Error listing observation notes: ${error.message}`);
        return { success: false, error: error.message };
      }

      // Transform data for list view (simplified for now)
      const listItems: ObservationNoteListItem[] =
        notes?.map((note) => ({
          id: note.id,
          title: note.title,
          content: note.content,
          status: note.status,
          created_at: note.created_at || "",
          updated_at: note.updated_at || "",
          appointment_title: "Appointment", // TODO: Fetch from appointment
          appointment_date: "", // TODO: Fetch from appointment
          case_number: "", // TODO: Fetch from case file
          created_by_email: "", // TODO: Fetch from user
        })) || [];

      return { success: true, data: listItems };
    } catch (error) {
      logger.error(`Unexpected error listing observation notes: ${error}`);
      return { success: false, error: `Unexpected error: ${error}` };
    }
  }

  /**
   * Get appointments that can have observation notes (those with case_file_id)
   */
  static async getAvailableAppointments(): Promise<
    ObservationNoteServiceResponse<
      Array<{ id: string; title: string; appointment_date: string; case_number: string }>
    >
  > {
    try {
      const supabase = await createClient();
      const currentUser = await auth.getCurrentUser();

      if (!currentUser) {
        return { success: false, error: "User not authenticated" };
      }

      const organizationId = await auth.getCurrentUserOrganizationId();
      if (!organizationId) {
        return { success: false, error: "User organization not found" };
      }

      const { data: appointments, error } = await supabase
        .from("appointments")
        .select(
          `
          id,
          title,
          appointment_date,
          case_files (
            case_number
          )
        `
        )
        .eq("organization_id", organizationId)
        .not("case_file_id", "is", null)
        .order("appointment_date", { ascending: false });

      if (error) {
        logger.error(`Error fetching available appointments: ${error.message}`);
        return { success: false, error: error.message };
      }

      const availableAppointments =
        appointments?.map((apt) => ({
          id: apt.id,
          title: apt.title,
          appointment_date: apt.appointment_date,
          case_number: "Case File", // TODO: Fetch case number from case_files table
        })) || [];

      return { success: true, data: availableAppointments };
    } catch (error) {
      logger.error(`Unexpected error fetching available appointments: ${error}`);
      return { success: false, error: `Unexpected error: ${error}` };
    }
  }
}
