import { ReactNode } from "react";

interface ObservationNotesLayoutProps {
  children: ReactNode;
}

export default function ObservationNotesLayout({ children }: ObservationNotesLayoutProps) {
  return <div className="observation-notes-domain">{children}</div>;
}

export const metadata = {
  title: "Observation Notes",
  description: "Manage observation notes for appointments related to case files",
};
