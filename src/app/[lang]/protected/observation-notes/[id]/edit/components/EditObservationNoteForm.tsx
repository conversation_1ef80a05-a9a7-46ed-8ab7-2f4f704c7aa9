"use client";

import { useState, useActionState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { RichTextEditor } from "@/components/ui/rich-text-editor";
import { showNotification } from "@/components/ui/notification";
import { editObservationNote } from "../../../actions/edit";
import { ObservationNoteWithRelations } from "../../../lib/types";

interface EditObservationNoteFormProps {
  lang: string;
  note: ObservationNoteWithRelations;
}

export default function EditObservationNoteForm({ lang, note }: EditObservationNoteFormProps) {
  const [title, setTitle] = useState<string>(note.title);
  const [content, setContent] = useState<string>(note.content);
  const [status, setStatus] = useState<string>(note.status);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [state, formAction] = useActionState(editObservationNote, {
    success: false,
    error: "",
    data: null,
  });

  const handleSubmit = async (formData: FormData) => {
    if (!title.trim()) {
      showNotification({
        title: "Validation Error",
        description: "Please enter a title",
        type: "error",
      });
      return;
    }

    if (!content.trim()) {
      showNotification({
        title: "Validation Error",
        description: "Please enter content for the note",
        type: "error",
      });
      return;
    }

    setIsSubmitting(true);

    // Add the form data
    formData.append("lang", lang);
    formData.append("id", note.id);
    formData.append("title", title);
    formData.append("content", content);
    formData.append("status", status);

    try {
      formAction(formData);
    } catch (error) {
      showNotification({
        title: "Error",
        description: "Failed to update observation note",
        type: "error",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Show error notification if there's an error
  if (state.error) {
    showNotification({
      title: "Error",
      description: state.error,
      type: "error",
    });
  }

  return (
    <form action={handleSubmit} className="space-y-6">
      {/* Appointment Info (Read-only) */}
      <div className="space-y-2">
        <Label>Appointment</Label>
        <div className="p-3 bg-muted rounded-md">
          <div className="font-medium">{note.appointment?.title}</div>
          <div className="text-sm text-muted-foreground">
            {note.appointment?.appointment_date
              ? new Date(note.appointment.appointment_date).toLocaleDateString()
              : "N/A"}
            {note.case_file && ` - Case: ${note.case_file.case_number}`}
          </div>
        </div>
        <p className="text-sm text-muted-foreground">
          The appointment cannot be changed after the note is created.
        </p>
      </div>

      {/* Title */}
      <div className="space-y-2">
        <Label htmlFor="title">Title *</Label>
        <Input
          id="title"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          placeholder="Enter a title for the observation note..."
          maxLength={255}
          required
        />
      </div>

      {/* Status */}
      <div className="space-y-2">
        <Label htmlFor="status">Status</Label>
        <Select value={status} onValueChange={setStatus}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="draft">Draft</SelectItem>
            <SelectItem value="pending_approval">Pending Approval</SelectItem>
            <SelectItem value="approved">Approved</SelectItem>
            <SelectItem value="rejected">Rejected</SelectItem>
          </SelectContent>
        </Select>
        <p className="text-sm text-muted-foreground">Change the status of the observation note.</p>
      </div>

      {/* Content */}
      <div className="space-y-2">
        <Label htmlFor="content">Content *</Label>
        <RichTextEditor
          initialContent={content}
          onChange={setContent}
          placeholder="Write your observation notes here..."
          showToolbar
          showBubbleMenu
          height="400px"
        />
        <p className="text-sm text-muted-foreground">
          Use the rich text editor to format your observation notes with headings, lists, and other
          formatting.
        </p>
      </div>

      {/* Submit Button */}
      <div className="flex justify-end gap-2">
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? "Updating..." : "Update Note"}
        </Button>
      </div>
    </form>
  );
}
