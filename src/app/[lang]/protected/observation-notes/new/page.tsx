import { Suspense } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft } from "lucide-react";
import { getAvailableAppointments } from "../actions/list";
import { ROUTES } from "../lib/config/domain";
import CreateObservationNoteForm from "./components/CreateObservationNoteForm";

interface CreateObservationNotePageProps {
  params: {
    lang: string;
  };
}

export default async function CreateObservationNotePage({
  params,
}: CreateObservationNotePageProps) {
  const { lang } = params;

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="outline" size="sm" asChild>
          <Link href={`/${lang}${ROUTES.LIST}`}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Notes
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Create Observation Note</h1>
          <p className="text-muted-foreground">
            Create a new observation note for an appointment related to a case file
          </p>
        </div>
      </div>

      {/* Form */}
      <Card>
        <CardHeader>
          <CardTitle>New Observation Note</CardTitle>
          <CardDescription>
            Fill in the details below to create a new observation note. You can only create notes
            for appointments that are related to case files.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<div>Loading form...</div>}>
            <CreateObservationNoteFormWrapper lang={lang} />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  );
}

async function CreateObservationNoteFormWrapper({ lang }: { lang: string }) {
  const appointmentsResult = await getAvailableAppointments();

  if (!appointmentsResult.success) {
    return (
      <div className="text-center py-8">
        <p className="text-destructive mb-4">
          Error loading appointments: {appointmentsResult.error}
        </p>
        <Button variant="outline" asChild>
          <Link href={`/${lang}${ROUTES.LIST}`}>Back to Notes</Link>
        </Button>
      </div>
    );
  }

  const appointments = appointmentsResult.data || [];

  if (appointments.length === 0) {
    return (
      <div className="text-center py-8">
        <h3 className="text-lg font-semibold mb-2">No Available Appointments</h3>
        <p className="text-muted-foreground mb-4">
          There are no appointments related to case files available for creating observation notes.
        </p>
        <Button variant="outline" asChild>
          <Link href={`/${lang}${ROUTES.LIST}`}>Back to Notes</Link>
        </Button>
      </div>
    );
  }

  return <CreateObservationNoteForm lang={lang} appointments={appointments} />;
}
