"use client";

import { useState, useActionState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { RichTextEditor } from "@/components/ui/rich-text-editor";
import { showNotification } from "@/components/ui/notification";
import { createObservationNote } from "../../actions/create";

interface CreateObservationNoteFormProps {
  lang: string;
  appointments: Array<{
    id: string;
    title: string;
    appointment_date: string;
    case_number: string;
  }>;
}

export default function CreateObservationNoteForm({
  lang,
  appointments,
}: CreateObservationNoteFormProps) {
  const [selectedAppointment, setSelectedAppointment] = useState<string>("");
  const [title, setTitle] = useState<string>("");
  const [content, setContent] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [state, formAction] = useActionState(createObservationNote, {
    success: false,
    error: "",
    data: null,
  });

  const handleSubmit = async (formData: FormData) => {
    if (!selectedAppointment) {
      showNotification({
        title: "Validation Error",
        description: "Please select an appointment",
        type: "error",
      });
      return;
    }

    if (!title.trim()) {
      showNotification({
        title: "Validation Error",
        description: "Please enter a title",
        type: "error",
      });
      return;
    }

    if (!content.trim()) {
      showNotification({
        title: "Validation Error",
        description: "Please enter content for the note",
        type: "error",
      });
      return;
    }

    setIsSubmitting(true);

    // Add the form data
    formData.append("lang", lang);
    formData.append("appointment_id", selectedAppointment);
    formData.append("title", title);
    formData.append("content", content);

    try {
      formAction(formData);
    } catch (error) {
      showNotification({
        title: "Error",
        description: "Failed to create observation note",
        type: "error",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Show error notification if there's an error
  if (state.error) {
    showNotification({
      title: "Error",
      description: state.error,
      type: "error",
    });
  }

  return (
    <form action={handleSubmit} className="space-y-6">
      {/* Appointment Selection */}
      <div className="space-y-2">
        <Label htmlFor="appointment">Appointment *</Label>
        <Select value={selectedAppointment} onValueChange={setSelectedAppointment}>
          <SelectTrigger>
            <SelectValue placeholder="Select an appointment..." />
          </SelectTrigger>
          <SelectContent>
            {appointments.map((appointment) => (
              <SelectItem key={appointment.id} value={appointment.id}>
                <div className="flex flex-col">
                  <span className="font-medium">{appointment.title}</span>
                  <span className="text-sm text-muted-foreground">
                    {new Date(appointment.appointment_date).toLocaleDateString()} - Case:{" "}
                    {appointment.case_number}
                  </span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <p className="text-sm text-muted-foreground">
          Only appointments related to case files are available for observation notes.
        </p>
      </div>

      {/* Title */}
      <div className="space-y-2">
        <Label htmlFor="title">Title *</Label>
        <Input
          id="title"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          placeholder="Enter a title for the observation note..."
          maxLength={255}
          required
        />
      </div>

      {/* Content */}
      <div className="space-y-2">
        <Label htmlFor="content">Content *</Label>
        <RichTextEditor
          initialContent={content}
          onChange={setContent}
          placeholder="Write your observation notes here..."
          showToolbar
          showBubbleMenu
          height="400px"
        />
        <p className="text-sm text-muted-foreground">
          Use the rich text editor to format your observation notes with headings, lists, and other
          formatting.
        </p>
      </div>

      {/* Submit Button */}
      <div className="flex justify-end gap-2">
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? "Creating..." : "Create Note"}
        </Button>
      </div>
    </form>
  );
}
