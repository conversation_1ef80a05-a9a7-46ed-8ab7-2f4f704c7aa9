"use server";

import { ObservationNotesService } from "../lib/services/ObservationNotesService";
import { ActionState } from "@/lib/types/responses";
import { logger } from "@/lib/logger/services/LoggerService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { DOMAIN_PERMISSIONS } from "../lib/config/domain";
import { ObservationNoteWithRelations } from "../lib/types";

interface ViewParams {
  id: string;
}

/**
 * Get an observation note by ID
 */
export const viewObservationNote = requirePermission(DOMAIN_PERMISSIONS.VIEW)(async (
  _prevState: ActionState<ObservationNoteWithRelations>,
  params: ViewParams
): Promise<ActionState<ObservationNoteWithRelations>> => {
  try {
    const { id } = params;

    // Validate required fields
    if (!id) {
      return {
        success: false,
        error: "Observation note ID is required",
        data: null,
      };
    }

    // Get the observation note
    const result = await ObservationNotesService.view(id);

    if (!result.success) {
      logger.error(`Error retrieving observation note: ${result.error}`);
      return {
        success: false,
        error: result.error || "Failed to retrieve observation note",
        data: null,
      };
    }

    if (!result.data) {
      return {
        success: false,
        error: "Observation note not found",
        data: null,
      };
    }

    return {
      success: true,
      error: "",
      data: result.data,
    };
  } catch (error) {
    logger.error(`Unexpected error retrieving observation note: ${error}`);
    return {
      success: false,
      error: `An unexpected error occurred: ${error}`,
      data: null,
    };
  }
});
