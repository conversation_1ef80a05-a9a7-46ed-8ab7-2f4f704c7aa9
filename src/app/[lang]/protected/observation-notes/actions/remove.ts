"use server";

import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";
import { ObservationNotesService } from "../lib/services/ObservationNotesService";
import { ActionState } from "@/lib/types/responses";
import { logger } from "@/lib/logger/services/LoggerService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { DOMAIN_PERMISSIONS, ROUTES } from "../lib/config/domain";

/**
 * Remove an observation note (soft delete)
 */
export const removeObservationNote = requirePermission(DOMAIN_PERMISSIONS.DELETE)(async (
  _prevState: ActionState<any>,
  formData: FormData
): Promise<ActionState<any>> => {
  try {
    const lang = formData.get("lang") as string;
    const id = formData.get("id") as string;

    // Validate required fields
    if (!id) {
      return {
        success: false,
        error: "Observation note ID is required",
        data: null,
      };
    }

    // Remove the observation note
    const result = await ObservationNotesService.remove(id);

    if (!result.success) {
      logger.error(`Error removing observation note: ${result.error}`);
      return {
        success: false,
        error: result.error || "Failed to remove observation note",
        data: null,
      };
    }

    // Revalidate pages
    revalidatePath(`/${lang}${ROUTES.LIST}`);

    // Redirect to list
    redirect(`/${lang}${ROUTES.LIST}`);
  } catch (error) {
    logger.error(`Unexpected error removing observation note: ${error}`);
    return {
      success: false,
      error: `An unexpected error occurred: ${error}`,
      data: null,
    };
  }
});
