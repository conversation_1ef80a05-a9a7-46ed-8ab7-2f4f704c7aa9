"use server";

import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";
import { z } from "zod";
import { ObservationNotesService } from "../lib/services/ObservationNotesService";
import { ActionState } from "@/lib/types/responses";
import { logger } from "@/lib/logger/services/LoggerService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { DOMAIN_PERMISSIONS, ROUTES } from "../lib/config/domain";

// Validation schema
const createObservationNoteSchema = z.object({
  appointment_id: z.string().uuid("Invalid appointment ID"),
  title: z.string().min(1, "Title is required").max(255, "Title too long"),
  content: z.string().min(1, "Content is required"),
});

/**
 * Create a new observation note
 */
export const createObservationNote = requirePermission(DOMAIN_PERMISSIONS.CREATE)(async (
  _prevState: ActionState<any>,
  formData: FormData
): Promise<ActionState<any>> => {
  try {
    const lang = formData.get("lang") as string;

    // Extract and validate form data
    const rawData = {
      appointment_id: formData.get("appointment_id") as string,
      title: formData.get("title") as string,
      content: formData.get("content") as string,
    };

    const validationResult = createObservationNoteSchema.safeParse(rawData);

    if (!validationResult.success) {
      const errors = validationResult.error.errors.map((err) => err.message).join(", ");
      return {
        success: false,
        error: errors,
        data: null,
      };
    }

    const { appointment_id, title, content } = validationResult.data;

    // Create the observation note
    const result = await ObservationNotesService.create({
      attached_to_id: appointment_id,
      attached_to_type: "appointment",
      title,
      content,
      status: "draft",
    });

    if (!result.success) {
      logger.error(`Error creating observation note: ${result.error}`);
      return {
        success: false,
        error: result.error || "Failed to create observation note",
        data: null,
      };
    }

    // Revalidate pages
    revalidatePath(`/${lang}${ROUTES.LIST}`);

    // Redirect to the created note
    redirect(`/${lang}${ROUTES.VIEW(result.data!.id)}`);
  } catch (error) {
    logger.error(`Unexpected error creating observation note: ${error}`);
    return {
      success: false,
      error: `An unexpected error occurred: ${error}`,
      data: null,
    };
  }
});
