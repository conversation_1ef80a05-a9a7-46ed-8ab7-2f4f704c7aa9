"use server";

import { ObservationNotesService } from "../lib/services/ObservationNotesService";
import { ActionState } from "@/lib/types/responses";
import { logger } from "@/lib/logger/services/LoggerService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { DOMAIN_PERMISSIONS } from "../lib/config/domain";
import { ObservationNoteListItem, ObservationNoteFilters } from "../lib/types";

/**
 * List observation notes with optional filters
 */
export const listObservationNotes = requirePermission(DOMAIN_PERMISSIONS.VIEW)(async (
  filters: ObservationNoteFilters = {}
): Promise<ActionState<ObservationNoteListItem[]>> => {
  try {
    // Get the observation notes
    const result = await ObservationNotesService.list(filters);

    if (!result.success) {
      logger.error(`Error listing observation notes: ${result.error}`);
      return {
        success: false,
        error: result.error || "Failed to list observation notes",
        data: [],
      };
    }

    return {
      success: true,
      error: "",
      data: result.data || [],
    };
  } catch (error) {
    logger.error(`Unexpected error listing observation notes: ${error}`);
    return {
      success: false,
      error: `An unexpected error occurred: ${error}`,
      data: [],
    };
  }
});

/**
 * Get available appointments for creating observation notes
 */
export const getAvailableAppointments = requirePermission(DOMAIN_PERMISSIONS.VIEW)(
  async (): Promise<
    ActionState<Array<{ id: string; title: string; appointment_date: string; case_number: string }>>
  > => {
    try {
      // Get available appointments
      const result = await ObservationNotesService.getAvailableAppointments();

      if (!result.success) {
        logger.error(`Error fetching available appointments: ${result.error}`);
        return {
          success: false,
          error: result.error || "Failed to fetch available appointments",
          data: [],
        };
      }

      return {
        success: true,
        error: "",
        data: result.data || [],
      };
    } catch (error) {
      logger.error(`Unexpected error fetching available appointments: ${error}`);
      return {
        success: false,
        error: `An unexpected error occurred: ${error}`,
        data: [],
      };
    }
  }
);
