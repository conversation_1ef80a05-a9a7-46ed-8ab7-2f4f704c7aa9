"use server";

import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";
import { z } from "zod";
import { ObservationNotesService } from "../lib/services/ObservationNotesService";
import { ActionState } from "@/lib/types/responses";
import { logger } from "@/lib/logger/services/LoggerService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { DOMAIN_PERMISSIONS, ROUTES } from "../lib/config/domain";

// Validation schema
const editObservationNoteSchema = z.object({
  id: z.string().uuid("Invalid observation note ID"),
  title: z.string().min(1, "Title is required").max(255, "Title too long"),
  content: z.string().min(1, "Content is required"),
  status: z.enum(["draft", "pending_approval", "approved", "rejected"]).optional(),
});

/**
 * Update an observation note
 */
export const editObservationNote = requirePermission(DOMAIN_PERMISSIONS.UPDATE)(async (
  _prevState: ActionState<any>,
  formData: FormData
): Promise<ActionState<any>> => {
  try {
    const lang = formData.get("lang") as string;
    const id = formData.get("id") as string;

    // Extract and validate form data
    const rawData = {
      id,
      title: formData.get("title") as string,
      content: formData.get("content") as string,
      status: (formData.get("status") as string) || undefined,
    };

    const validationResult = editObservationNoteSchema.safeParse(rawData);

    if (!validationResult.success) {
      const errors = validationResult.error.errors.map((err) => err.message).join(", ");
      return {
        success: false,
        error: errors,
        data: null,
      };
    }

    const { title, content, status } = validationResult.data;

    // Update the observation note
    const updateData: any = { title, content };
    if (status) {
      updateData.status = status;
    }

    const result = await ObservationNotesService.update(id, updateData);

    if (!result.success) {
      logger.error(`Error updating observation note: ${result.error}`);
      return {
        success: false,
        error: result.error || "Failed to update observation note",
        data: null,
      };
    }

    // Revalidate pages
    revalidatePath(`/${lang}${ROUTES.LIST}`);
    revalidatePath(`/${lang}${ROUTES.VIEW(id)}`);

    // Redirect to the updated note
    redirect(`/${lang}${ROUTES.VIEW(id)}`);
  } catch (error) {
    logger.error(`Unexpected error updating observation note: ${error}`);
    return {
      success: false,
      error: `An unexpected error occurred: ${error}`,
      data: null,
    };
  }
});
