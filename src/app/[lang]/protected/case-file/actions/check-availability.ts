"use server";

import { createClient } from "@/lib/supabase/server";

export async function checkEmployeeAvailabilityAction(
  employeeId: string,
  date: string,
  startTime: string,
  endTime: string,
  organizationId: string,
  excludeAppointmentId?: string // Optional: exclude this appointment from conflict checking (for editing)
): Promise<{ success: boolean; available: boolean; error?: string; conflictDetails?: string }> {
  try {
    const supabase = await createClient();
    const dayOfWeek = new Date(date).getDay();
    const requestedStart = new Date(`${date}T${startTime}`);
    const requestedEnd = new Date(`${date}T${endTime}`);

    // Step 1: Check regular employee availability schedule
    const { data: regularAvailability, error: availabilityError } = await supabase
      .from("employee_availability")
      .select("*")
      .eq("employee_id", employeeId)
      .eq("organization_id", organizationId)
      .eq("day_of_week", dayOfWeek)
      .lte("start_time", startTime)
      .gte("end_time", endTime);

    if (availabilityError) {
      console.warn("Failed to check regular availability:", availabilityError);
    }

    const hasRegularAvailability = regularAvailability && regularAvailability.length > 0;

    // Step 2: Check availability exceptions for this specific date
    const { data: exceptions, error: exceptionsError } = await supabase
      .from("employee_availability_exceptions")
      .select("*")
      .eq("employee_id", employeeId)
      .eq("organization_id", organizationId)
      .eq("exception_date", date);

    if (exceptionsError) {
      console.warn("Failed to check availability exceptions:", exceptionsError);
    }

    // Check if there are exceptions that affect this time slot
    const relevantExceptions =
      exceptions?.filter((exception) => {
        const exceptionStart = new Date(`${date}T${exception.start_time}`);
        const exceptionEnd = new Date(`${date}T${exception.end_time}`);

        // Check if the requested time overlaps with the exception
        return requestedStart < exceptionEnd && requestedEnd > exceptionStart;
      }) || [];

    // If there are exceptions, they override regular availability
    if (relevantExceptions.length > 0) {
      const hasAvailableException = relevantExceptions.some((exception) => exception.is_available);
      if (!hasAvailableException) {
        const exception = relevantExceptions[0];
        return {
          success: true,
          available: false,
          error: "Employee is not available due to schedule exception",
          conflictDetails: exception.reason ? `Reason: ${exception.reason}` : undefined,
        };
      }
    } else if (!hasRegularAvailability) {
      // No exceptions and no regular availability
      return {
        success: true,
        available: false,
        error: "Employee is not available according to their regular schedule",
      };
    }

    // Step 3: Check for approved time off
    const { data: timeOff, error: timeOffError } = await supabase
      .from("employee_time_off")
      .select("*")
      .eq("employee_id", employeeId)
      .eq("organization_id", organizationId)
      .eq("status", "approved")
      .lte("start_date", date)
      .gte("end_date", date);

    if (timeOffError) {
      console.warn("Failed to check time off:", timeOffError);
    }

    // Check if any time off affects this time slot
    const conflictingTimeOff =
      timeOff?.filter((timeOffRecord) => {
        // If no specific times are set, the entire day is off
        if (!timeOffRecord.start_time || !timeOffRecord.end_time) {
          return true;
        }

        const timeOffStart = new Date(`${date}T${timeOffRecord.start_time}`);
        const timeOffEnd = new Date(`${date}T${timeOffRecord.end_time}`);

        return requestedStart < timeOffEnd && requestedEnd > timeOffStart;
      }) || [];

    if (conflictingTimeOff.length > 0) {
      const timeOffRecord = conflictingTimeOff[0];
      return {
        success: true,
        available: false,
        error: "Employee is on approved time off",
        conflictDetails: `${timeOffRecord.type.replace("_", " ")} - ${timeOffRecord.description || "No description"}`,
      };
    }

    // Step 4: Check for appointment assignment conflicts
    // Query for conflicting appointments
    const conflictQuery = supabase
      .from("appointment_assignments")
      .select(
        `
        id,
        appointments!appointment_assignments_appointment_id_fkey(
          id,
          title,
          appointment_date,
          start_time,
          end_time
        )
      `
      )
      .eq("employee_id", employeeId)
      .eq("organization_id", organizationId);

    const { data: assignments, error: queryError } = await conflictQuery;

    if (queryError) {
      console.warn("Failed to check appointment conflicts:", queryError);
      // If we can't check conflicts, allow the assignment but warn
      return {
        success: true,
        available: true,
        error: "Could not verify appointment conflicts, but allowing assignment",
      };
    }

    // Check for time conflicts using the already declared variables
    const conflicts =
      assignments?.filter((assignment: any) => {
        const appointment = assignment.appointments;
        if (!appointment) return false;

        // Skip the current appointment if we're editing
        if (excludeAppointmentId && appointment.id === excludeAppointmentId) {
          return false;
        }

        // Only check appointments on the same date
        if (appointment.appointment_date !== date) {
          return false;
        }

        const appointmentStart = new Date(
          `${appointment.appointment_date}T${appointment.start_time}`
        );
        const appointmentEnd = new Date(`${appointment.appointment_date}T${appointment.end_time}`);

        // Check for time overlap
        return requestedStart < appointmentEnd && requestedEnd > appointmentStart;
      }) || [];

    if (conflicts.length > 0) {
      const conflictingAppointment = conflicts[0].appointments;
      return {
        success: true,
        available: false,
        error: "Employee has a conflicting appointment",
        conflictDetails: `Conflicts with "${conflictingAppointment.title}" from ${conflictingAppointment.start_time} to ${conflictingAppointment.end_time}`,
      };
    }

    // No conflicts found
    return {
      success: true,
      available: true,
    };
  } catch (error) {
    return {
      success: false,
      available: false,
      error: error instanceof Error ? error.message : "Failed to check availability",
    };
  }
}
