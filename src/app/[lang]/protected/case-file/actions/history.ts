"use server";

import { createClient } from "@/lib/supabase/server";
import { logger } from "@/lib/logger/services/LoggerService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { DOMAIN_PERMISSIONS } from "../lib/config/domain";
import {
  HistoryItem,
  HistoryAction,
  HistoryChange,
} from "@/components/ui/history-timeline/history-timeline";

/**
 * Fetch history for a specific case file
 * @param caseFileId Case File ID
 * @returns Array of history items formatted for the HistoryTimeline component
 */
export const fetchCaseFileHistory = requirePermission(DOMAIN_PERMISSIONS.VIEW)(async (
  caseFileId: string
): Promise<HistoryItem[]> => {
  try {
    const supabase = await createClient();

    // Get case file history with user information
    const { data: historyData, error } = await supabase
      .from("case_file_history")
      .select(
        `
        id,
        user_id,
        action,
        changes,
        created_at
      `
      )
      .eq("case_file_id", caseFileId)
      .order("created_at", { ascending: false });

    if (error) {
      logger.error(`Error fetching case file history: ${error.message}`);
      return [];
    }

    if (!historyData || historyData.length === 0) {
      return [];
    }

    // Get user information for all unique user IDs
    const { data: userData, error: userError } = await supabase.auth.admin.listUsers();

    if (userError) {
      logger.warn(`Error fetching user data: ${userError.message}`);
    }

    // Create a map of user ID to user info
    const userMap = new Map();
    if (userData?.users) {
      userData.users.forEach((user) => {
        userMap.set(user.id, {
          id: user.id,
          name: user.user_metadata?.name || user.email || "Unknown User",
          email: user.email,
        });
      });
    }

    // Convert history data to HistoryItem format
    const historyItems: HistoryItem[] = historyData.map((item) => {
      const user = userMap.get(item.user_id) || {
        id: item.user_id,
        name: "Unknown User",
        email: "",
      };

      // Determine action type
      let action: HistoryAction = "update";
      if (item.action === "created") {
        action = "create";
      } else if (item.action.includes("status_changed_to_closed")) {
        action = "delete";
      }

      // Parse changes and create change items
      const changes: HistoryChange[] = [];
      if (item.changes && typeof item.changes === "object") {
        const changesObj = item.changes as any;

        // Handle status changes
        if (changesObj.from_status && changesObj.to_status) {
          changes.push({
            field: "Status",
            oldValue: changesObj.from_status,
            newValue: changesObj.to_status,
          });
        }

        // Handle other changes
        Object.entries(changesObj).forEach(([key, value]) => {
          if (key !== "from_status" && key !== "to_status" && key !== "notes") {
            if (typeof value === "object" && value !== null && "from" in value && "to" in value) {
              const changeValue = value as { from: any; to: any };
              changes.push({
                field: formatFieldName(key),
                oldValue: changeValue.from,
                newValue: changeValue.to,
              });
            }
          }
        });
      }

      // Create summary
      const summary = formatActionSummary(item.action, item.changes);

      return {
        id: item.id,
        timestamp: item.created_at || new Date().toISOString(),
        user: {
          id: user.id,
          name: user.name,
          avatarUrl: undefined,
        },
        action,
        changes: changes.length > 0 ? changes : undefined,
        summary,
      };
    });

    return historyItems;
  } catch (error) {
    logger.error(`Error in fetchCaseFileHistory: ${error}`);
    return [];
  }
});

/**
 * Format field name for display
 */
function formatFieldName(field: string): string {
  return field
    .replace(/_/g, " ")
    .replace(/([A-Z])/g, " $1")
    .replace(/^./, (str) => str.toUpperCase());
}

/**
 * Format action summary for display
 */
function formatActionSummary(action: string, changes: any): string {
  if (action === "created") {
    return "Case file created";
  }

  if (action.includes("status_changed")) {
    const changesObj = changes as any;
    if (changesObj?.from_status && changesObj?.to_status) {
      return `Status changed from ${changesObj.from_status} to ${changesObj.to_status}`;
    }
    return "Status changed";
  }

  if (action.includes("assignment")) {
    return "Assignment changed";
  }

  if (action.includes("contact")) {
    return "Contact information updated";
  }

  if (action.includes("document")) {
    return "Document attached";
  }

  // Default formatting
  return action.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());
}
