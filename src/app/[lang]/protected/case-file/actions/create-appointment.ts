"use server";

import { z } from "zod";
import { revalidatePath } from "next/cache";
import { auth } from "@/lib/authentication/services/AuthenticationService";
import { createClient } from "@/lib/supabase/server";
import { saveAppointmentAssignments } from "./appointment-assignments";
import { saveAppointmentRoom } from "./appointment-rooms";

// Validation schema for case file appointment creation
const createCaseFileAppointmentSchema = z.object({
  title: z.string().min(1, "Title is required").max(255, "Title is too long"),
  description: z.string().max(1000, "Description is too long").optional(),
  appointment_date: z.string().min(1, "Date is required"),
  start_time: z.string().min(1, "Start time is required"),
  end_time: z.string().min(1, "End time is required"),
  case_file_id: z.string().min(1, "Case file ID is required"),
});

/**
 * Create a new appointment linked to a case file
 */
export async function createCaseFileAppointment(
  formData: FormData,
  assignments?: Array<{ employeeId: string; assignmentRole?: string }>,
  roomId?: string
) {
  try {
    // Get current user and organization
    const currentUser = await auth.getCurrentUser();
    if (!currentUser) {
      return { success: false, error: "Authentication required" };
    }

    const userProfile = await auth.getCurrentUserProfile();
    if (!userProfile?.organizationId) {
      return { success: false, error: "Organization context required" };
    }

    // Extract and validate form data
    const rawData = {
      title: formData.get("title") as string,
      description: formData.get("description") as string,
      appointment_date: formData.get("appointment_date") as string,
      start_time: formData.get("start_time") as string,
      end_time: formData.get("end_time") as string,
      case_file_id: formData.get("case_file_id") as string,
    };

    const validatedData = createCaseFileAppointmentSchema.parse(rawData);

    // Validate time logic
    const startTime = new Date(`2000-01-01T${validatedData.start_time}`);
    const endTime = new Date(`2000-01-01T${validatedData.end_time}`);

    if (endTime <= startTime) {
      return { success: false, error: "End time must be after start time" };
    }

    // Calculate duration in minutes
    const durationMinutes = Math.round((endTime.getTime() - startTime.getTime()) / (1000 * 60));

    // Validate appointment date is not in the past
    const appointmentDate = new Date(validatedData.appointment_date);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (appointmentDate < today) {
      return { success: false, error: "Cannot create appointments in the past" };
    }

    // Create appointment directly in database
    const supabase = await createClient();
    const { data, error } = await supabase
      .from("appointments")
      .insert({
        title: validatedData.title,
        description: validatedData.description || null,
        appointment_date: validatedData.appointment_date,
        start_time: validatedData.start_time,
        end_time: validatedData.end_time,
        duration_minutes: durationMinutes,
        status: "planned",
        case_file_id: validatedData.case_file_id,
        organization_id: userProfile.organizationId,
        created_by: currentUser.id,
      })
      .select()
      .single();

    if (error) {
      return { success: false, error: `Failed to create appointment: ${error.message}` };
    }

    // Save employee assignments if provided
    if (assignments && assignments.length > 0) {
      const assignmentResult = await saveAppointmentAssignments(
        data.id,
        assignments,
        userProfile.organizationId
      );

      if (!assignmentResult.success) {
        // Log the error but don't fail the appointment creation
        console.error("Failed to save assignments:", assignmentResult.error);
      }
    }

    // Save room assignment if provided
    if (roomId) {
      const roomResult = await saveAppointmentRoom(data.id, roomId, userProfile.organizationId);

      if (!roomResult.success) {
        // Log the error but don't fail the appointment creation
        console.error("Failed to save room assignment:", roomResult.error);
      }
    }

    // Revalidate case file page
    revalidatePath(`/protected/case-file/${validatedData.case_file_id}/active`);

    return {
      success: true,
      data,
      message: "Case file appointment created successfully",
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const fieldErrors = error.errors
        .map((err) => `${err.path.join(".")}: ${err.message}`)
        .join(", ");
      return { success: false, error: `Validation failed: ${fieldErrors}` };
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to create case file appointment",
    };
  }
}

// Update schema for editing appointments
const updateCaseFileAppointmentSchema = z.object({
  appointment_id: z.string().min(1, "Appointment ID is required"),
  title: z.string().min(1, "Title is required").max(255, "Title is too long"),
  description: z.string().max(1000, "Description is too long").optional(),
  appointment_date: z.string().min(1, "Date is required"),
  start_time: z.string().min(1, "Start time is required"),
  end_time: z.string().min(1, "End time is required"),
});

/**
 * Update an existing appointment
 */
export async function updateCaseFileAppointment(
  formData: FormData,
  assignments?: Array<{ employeeId: string; assignmentRole?: string }>,
  roomId?: string
) {
  try {
    // Get current user and organization
    const currentUser = await auth.getCurrentUser();
    if (!currentUser) {
      return { success: false, error: "Authentication required" };
    }

    const userProfile = await auth.getCurrentUserProfile();
    if (!userProfile?.organizationId) {
      return { success: false, error: "Organization context required" };
    }

    // Extract and validate form data
    const rawData = {
      appointment_id: formData.get("appointment_id") as string,
      title: formData.get("title") as string,
      description: formData.get("description") as string,
      appointment_date: formData.get("appointment_date") as string,
      start_time: formData.get("start_time") as string,
      end_time: formData.get("end_time") as string,
    };

    const validatedData = updateCaseFileAppointmentSchema.parse(rawData);

    // Validate time logic
    const startTime = new Date(`2000-01-01T${validatedData.start_time}`);
    const endTime = new Date(`2000-01-01T${validatedData.end_time}`);

    if (endTime <= startTime) {
      return { success: false, error: "End time must be after start time" };
    }

    // Calculate duration in minutes
    const durationMinutes = Math.round((endTime.getTime() - startTime.getTime()) / (1000 * 60));

    // Update appointment in database
    const supabase = await createClient();

    const { data, error } = await supabase
      .from("appointments")
      .update({
        title: validatedData.title,
        description: validatedData.description || null,
        appointment_date: validatedData.appointment_date,
        start_time: validatedData.start_time,
        end_time: validatedData.end_time,
        duration_minutes: durationMinutes,
        updated_at: new Date().toISOString(),
      })
      .eq("id", validatedData.appointment_id)
      .eq("organization_id", userProfile.organizationId)
      .select()
      .single();

    if (error) {
      return { success: false, error: `Failed to update appointment: ${error.message}` };
    }

    // Save employee assignments if provided
    if (assignments !== undefined) {
      const assignmentResult = await saveAppointmentAssignments(
        validatedData.appointment_id,
        assignments,
        userProfile.organizationId
      );

      if (!assignmentResult.success) {
        // Log the error but don't fail the appointment update
        console.error("Failed to save assignments:", assignmentResult.error);
      }
    }

    // Save room assignment if provided (undefined means don't change, null means remove)
    if (roomId !== undefined) {
      const roomResult = await saveAppointmentRoom(
        validatedData.appointment_id,
        roomId,
        userProfile.organizationId
      );

      if (!roomResult.success) {
        // Log the error but don't fail the appointment update
        console.error("Failed to save room assignment:", roomResult.error);
      }
    }

    // Revalidate case file page
    revalidatePath(`/protected/case-file`);

    return {
      success: true,
      data,
      message: "Appointment updated successfully",
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const fieldErrors = error.errors
        .map((err) => `${err.path.join(".")}: ${err.message}`)
        .join(", ");
      return { success: false, error: `Validation failed: ${fieldErrors}` };
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to update appointment",
    };
  }
}

/**
 * Update appointment status
 */
export async function updateAppointmentStatus(
  appointmentId: string,
  status:
    | "planned"
    | "confirmed"
    | "in_progress"
    | "completed"
    | "missed"
    | "postponed"
    | "cancelled"
) {
  try {
    // Get current user and organization
    const currentUser = await auth.getCurrentUser();
    if (!currentUser) {
      return { success: false, error: "Authentication required" };
    }

    const userProfile = await auth.getCurrentUserProfile();
    if (!userProfile?.organizationId) {
      return { success: false, error: "Organization context required" };
    }

    // Update appointment status in database
    const supabase = await createClient();

    const { data, error } = await supabase
      .from("appointments")
      .update({
        status: status,
        updated_at: new Date().toISOString(),
      })
      .eq("id", appointmentId)
      .eq("organization_id", userProfile.organizationId)
      .select()
      .single();

    if (error) {
      return { success: false, error: `Failed to update appointment status: ${error.message}` };
    }

    // Revalidate case file page
    revalidatePath(`/protected/case-file`);

    return {
      success: true,
      data,
      message: `Appointment marked as ${status}`,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to update appointment status",
    };
  }
}
