import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

import { P } from "@/components/typography";
import { Users, ArrowRight, Phone, Mail } from "lucide-react";
import { CaseFileDashboardData } from "../../lib/types";
import Link from "next/link";

interface FamilyInformationProps {
  familyInfo: CaseFileDashboardData["familyInfo"];
  contacts: Array<{
    id: string;
    name: string;
    email?: any;
    phone?: any;
    relationshipType: string;
  }>;
  caseFileId: string;
  lang: string;
  dictionary?: {
    title: string;
    description: string;
    children?: string;
    adults?: string;
    professionals?: string;
    noContacts?: string;
    noContactsDescription?: string;
    viewAllContacts?: string;
  };
}

/**
 * Family Information component for case file dashboard
 * Displays case file contacts organized by relationship type
 */
export function FamilyInformation({
  familyInfo,
  contacts,
  caseFileId,
  lang,
  dictionary,
}: FamilyInformationProps) {
  // Default translations
  const defaultDictionary = {
    title: "Family Information",
    description: "Contact details and relationships",
    children: "Children",
    adults: "Adults",
    professionals: "Professionals",
    noContacts: "No contacts found",
    noContactsDescription: "Add contacts to get started",
    viewAllContacts: "View All Contacts",
  };

  const t = {
    ...defaultDictionary,
    ...dictionary,
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center">
            <Users className="h-5 w-5 mr-2" />
            {t.title}
          </div>
          <Badge variant="outline" className="text-xs">
            {contacts.length} Contacts
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {contacts.length > 0 ? (
          <div className="space-y-3">
            {/* Contact list with quick contact info */}
            {contacts.map((contact) => (
              <div key={contact.id} className="space-y-1">
                {/* Contact name and relationship */}
                <div className="flex items-center gap-2 text-sm">
                  <span className="text-muted-foreground">•</span>
                  <span className="font-medium">{contact.name}</span>
                  <span className="text-xs text-muted-foreground capitalize">
                    ({contact.relationshipType.replace("_", " ")})
                  </span>
                </div>

                {/* Quick contact info */}
                {(contact.phone || contact.email) && (
                  <div className="flex items-center gap-3 pl-4 text-xs">
                    {contact.phone && (
                      <a
                        href={`tel:${contact.phone}`}
                        className="flex items-center gap-1 text-blue-600 hover:text-blue-800"
                      >
                        <Phone className="h-3 w-3" />
                        <span>{contact.phone}</span>
                      </a>
                    )}
                    {contact.email && (
                      <a
                        href={`mailto:${contact.email}`}
                        className="flex items-center gap-1 text-blue-600 hover:text-blue-800"
                      >
                        <Mail className="h-3 w-3" />
                        <span>{contact.email}</span>
                      </a>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="flex items-center gap-3 p-4 bg-muted/50 rounded-lg">
            <Users className="h-8 w-8 text-muted-foreground" />
            <div>
              <P className="font-medium text-muted-foreground">{t.noContacts}</P>
              <P className="text-xs text-muted-foreground">{t.noContactsDescription}</P>
            </div>
          </div>
        )}

        {/* Action Button */}
        <Button variant="outline" size="sm" className="w-full" asChild>
          <Link href={`/${lang}/protected/case-file/${caseFileId}/active?tab=contacts`}>
            {t.viewAllContacts} ({contacts.length})
            <ArrowRight className="h-4 w-4 ml-2" />
          </Link>
        </Button>
      </CardContent>
    </Card>
  );
}
