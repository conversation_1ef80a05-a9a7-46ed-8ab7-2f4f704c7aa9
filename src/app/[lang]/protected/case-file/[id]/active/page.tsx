import { notFound } from "next/navigation";
import { getCaseFileDashboardData, getCaseFileContacts } from "../../actions";
import { getCaseFileAppointments } from "../../actions/appointments";
import { CaseFileDashboard } from "../../components";
import { CaseFileNavigation } from "../../components/navigation";
import { PageHeader } from "@/components/PageHeader";
import { Button } from "@/components/ui/button";
import { Settings, Users, Calendar } from "lucide-react";
import { CaseFileDocuments } from "./components/CaseFileDocuments";
import { CaseFileHistory } from "./components/CaseFileHistory";
import { CaseFileContacts } from "./components/CaseFileContacts";
import { CaseFileAppointments } from "./components/CaseFileAppointments";
import { CaseFileServiceRequest } from "./components/CaseFileServiceRequest";
import { CaseFileTabs } from "./components/CaseFileTabs";
import CaseFileObservationNotes from "./components/CaseFileObservationNotes";
import { getDictionary } from "@/lib/i18n/cache";
import { TabsContent } from "@/components/ui/tabs";
import { EmployeeService } from "@/app/[lang]/protected/employee/lib/services/EmployeeService";
import { ProfileService } from "@/app/[lang]/protected/organization/(features)/profile/lib/services/ProfileService";

interface ActiveCaseFilePageProps {
  params: Promise<{
    lang: string;
    id: string;
  }>;
  searchParams: Promise<{
    tab?: string;
  }>;
}

/**
 * Active Case File Management Page
 * Central hub for managing ongoing family services
 */
export default async function ActiveCaseFilePage({
  params,
  searchParams,
}: ActiveCaseFilePageProps) {
  const { lang, id } = await params;
  const { tab } = await searchParams;

  // Determine active tab (default to dashboard)
  const activeTab = tab || "dashboard";

  // Get dictionary for translations
  const dictionary = await getDictionary();
  const t = dictionary.caseFile.active;

  // Get dashboard data using server action
  const dashboardResponse = await getCaseFileDashboardData(id);

  if (!dashboardResponse.success || !dashboardResponse.data) {
    notFound();
  }

  const { caseFile } = dashboardResponse.data;

  // Get case file contacts
  const contactsResponse = await getCaseFileContacts(id);
  const contacts = contactsResponse.success ? contactsResponse.data || [] : [];

  // Get case file appointments
  const appointmentsResponse = await getCaseFileAppointments(id);
  const appointments = appointmentsResponse.success ? appointmentsResponse.data || [] : [];

  // Get employees for appointment assignment
  const employeesResponse = await EmployeeService.listEmployees();
  const employees = employeesResponse.success ? employeesResponse.data || [] : [];

  // Get organization for availability checking
  const organization = await ProfileService.getCurrentOrganization();

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <PageHeader
        title={`${t.title} ${caseFile.caseNumber}`}
        description={t.description}
        actions={
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
            <Button variant="outline" size="sm">
              <Users className="h-4 w-4 mr-2" />
              Family
            </Button>
            <Button variant="outline" size="sm">
              <Calendar className="h-4 w-4 mr-2" />
              {t.dashboard.quickActions.schedule}
            </Button>
          </div>
        }
      />

      {/* Case File Navigation */}
      <CaseFileNavigation caseFileId={caseFile.id} currentStatus={caseFile.status} lang={lang} />

      {/* Tabbed Content */}
      <CaseFileTabs activeTab={activeTab} caseFileId={caseFile.id} lang={lang} dictionary={{
        ...t.tabs,
        observationNotes: "Observation Notes" // TODO: Add to i18n
      }}>
        <TabsContent value="dashboard" className="space-y-6">
          <CaseFileDashboard
            data={dashboardResponse.data}
            appointments={appointments}
            lang={lang}
            dictionary={t.dashboard}
          />
        </TabsContent>

        <TabsContent value="service-request" className="space-y-6">
          <CaseFileServiceRequest
            caseFile={caseFile}
            caseFileId={caseFile.id}
            lang={lang}
            dictionary={t.serviceRequest}
          />
        </TabsContent>

        <TabsContent value="documents" className="space-y-6">
          <CaseFileDocuments caseFileId={caseFile.id} lang={lang} dictionary={t.documents} />
        </TabsContent>

        <TabsContent value="contacts" className="space-y-6">
          <CaseFileContacts
            contacts={contacts}
            caseFileId={caseFile.id}
            lang={lang}
            dictionary={t.contacts}
          />
        </TabsContent>

        <TabsContent value="appointments" className="space-y-6">
          <CaseFileAppointments
            caseFileId={caseFile.id}
            caseFile={caseFile}
            appointments={appointments}
            employees={Array.isArray(employees) ? employees : []}
            organizationId={organization?.id || null}
            lang={lang}
            dictionary={t.appointments}
          />
        </TabsContent>

        <TabsContent value="observation-notes" className="space-y-6">
          <CaseFileObservationNotes
            caseFileId={caseFile.id}
            lang={lang}
            dictionary={{
              title: "Observation Notes",
              description: "View and manage observation notes for this case file",
              createNote: "Create Note",
              noNotes: "No observation notes yet",
              noNotesDescription: "Create your first observation note for an appointment in this case file.",
              viewNote: "View",
              createdBy: "Created by",
              createdAt: "Created",
              status: "Status"
            }}
          />
        </TabsContent>

        <TabsContent value="history" className="space-y-6">
          <CaseFileHistory caseFileId={caseFile.id} lang={lang} dictionary={t.history} />
        </TabsContent>
      </CaseFileTabs>
    </div>
  );
}
