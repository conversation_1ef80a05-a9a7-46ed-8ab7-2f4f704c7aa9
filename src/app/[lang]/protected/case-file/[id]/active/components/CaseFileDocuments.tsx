"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { DocumentList } from "@/components/ui/document-list";
import { FileText, Users } from "lucide-react";
import { motion } from "framer-motion";

interface CaseFileDocumentsProps {
  caseFileId: string;
  lang: string;
  dictionary?: {
    title: string;
    caseDocuments: string;
    contactDocuments: string;
    noDocuments: string;
    noDocumentsDescription: string;
    uploadDocument: string;
    viewDocument: string;
    downloadDocument: string;
    deleteDocument: string;
    documentUploaded: string;
    documentDeleted: string;
  };
}

export function CaseFileDocuments({ caseFileId, dictionary }: CaseFileDocumentsProps) {
  // Default translations
  const defaultDictionary = {
    title: "Documents",
    caseDocuments: "Case Documents",
    contactDocuments: "Contact Documents",
    noDocuments: "No documents found",
    noDocumentsDescription: "Upload documents to get started",
    uploadDocument: "Upload Document",
    viewDocument: "View Document",
    downloadDocument: "Download Document",
    deleteDocument: "Delete Document",
    documentUploaded: "Document uploaded successfully",
    documentDeleted: "Document deleted successfully",
  };

  const t = dictionary || defaultDictionary;
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
    >
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            {t.title}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="case" className="space-y-4">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="case" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                {t.caseDocuments}
              </TabsTrigger>
              <TabsTrigger value="contacts" className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                {t.contactDocuments}
              </TabsTrigger>
            </TabsList>

            <TabsContent value="case" className="space-y-4">
              <DocumentList
                entityType="case_file"
                entityId={caseFileId}
                title={t.caseDocuments}
                description="Documents attached directly to this case file"
              />
            </TabsContent>

            <TabsContent value="contacts" className="space-y-4">
              <DocumentList
                entityType="case_file_contacts"
                entityId={caseFileId}
                title={t.contactDocuments}
                description="Documents attached to contacts in this case file"
                allowUpload={false}
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </motion.div>
  );
}
