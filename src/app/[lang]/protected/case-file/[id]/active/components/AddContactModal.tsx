"use client";

import { useState, useActionState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ContactSearchCombobox } from "@/app/[lang]/protected/contact/(features)/management/components/ContactSearchCombobox";
import { addContactToCaseFile } from "../actions/contact-management";
import { ActionState } from "@/lib/types/responses";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2 } from "lucide-react";

interface AddContactModalProps {
  isOpen: boolean;
  onClose: () => void;
  caseFileId: string;
  lang: string;
  dictionary?: {
    title: string;
    description: string;
    searchPlaceholder: string;
    relationshipType: string;
    selectRelationshipType: string;
    addContact: string;
    adding: string;
    cancel: string;
  };
  relationshipTypes?: {
    parent: string;
    child: string;
    guardian: string;
    sibling: string;
    grandparent: string;
    other_family: string;
    social_worker: string;
    therapist: string;
    teacher: string;
    doctor: string;
    lawyer: string;
    other_professional: string;
    friend: string;
    neighbor: string;
    other: string;
  };
}

/**
 * Modal for adding contacts to a case file
 */
export function AddContactModal({
  isOpen,
  onClose,
  caseFileId,
  lang,
  dictionary,
  relationshipTypes,
}: AddContactModalProps) {
  const [selectedContactId, setSelectedContactId] = useState<string>("");
  const [selectedRelationshipType, setSelectedRelationshipType] = useState<string>("");

  // Default translations
  const defaultDictionary = {
    title: "Add Contact to Case File",
    description:
      "Search for a contact and specify their relationship to add them to this case file.",
    searchPlaceholder: "Search for a contact...",
    relationshipType: "Relationship Type",
    selectRelationshipType: "Select relationship type",
    addContact: "Add Contact",
    adding: "Adding...",
    cancel: "Cancel",
  };

  const defaultRelationshipTypes = {
    parent: "Parent",
    child: "Child",
    guardian: "Guardian",
    sibling: "Sibling",
    grandparent: "Grandparent",
    other_family: "Other Family",
    social_worker: "Social Worker",
    therapist: "Therapist",
    teacher: "Teacher",
    doctor: "Doctor",
    lawyer: "Lawyer",
    other_professional: "Other Professional",
    friend: "Friend",
    neighbor: "Neighbor",
    other: "Other",
  };

  const t = dictionary || defaultDictionary;
  const relationshipLabels = relationshipTypes || defaultRelationshipTypes;

  // Form state management
  const initialState: ActionState<any> = {
    success: true,
    error: "",
    data: null,
  };

  const [state, formAction, isPending] = useActionState(addContactToCaseFile, initialState);

  // Handle form submission
  const handleSubmit = async (formData: FormData) => {
    // Add the selected values to form data
    formData.append("caseFileId", caseFileId);
    formData.append("contactId", selectedContactId);
    formData.append("relationshipType", selectedRelationshipType);
    formData.append("lang", lang);

    // Submit the form
    await formAction(formData);

    // Close modal on success
    if (state.success && !state.error) {
      handleClose();
    }
  };

  // Handle modal close
  const handleClose = () => {
    setSelectedContactId("");
    setSelectedRelationshipType("");
    onClose();
  };

  // Relationship type options
  const relationshipTypeOptions = [
    { value: "parent", label: relationshipLabels.parent },
    { value: "child", label: relationshipLabels.child },
    { value: "guardian", label: relationshipLabels.guardian },
    { value: "sibling", label: relationshipLabels.sibling },
    { value: "grandparent", label: relationshipLabels.grandparent },
    { value: "other_family", label: relationshipLabels.other_family },
    { value: "social_worker", label: relationshipLabels.social_worker },
    { value: "therapist", label: relationshipLabels.therapist },
    { value: "teacher", label: relationshipLabels.teacher },
    { value: "doctor", label: relationshipLabels.doctor },
    { value: "lawyer", label: relationshipLabels.lawyer },
    { value: "other_professional", label: relationshipLabels.other_professional },
    { value: "friend", label: relationshipLabels.friend },
    { value: "neighbor", label: relationshipLabels.neighbor },
    { value: "other", label: relationshipLabels.other },
  ];

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{t.title}</DialogTitle>
          <DialogDescription>{t.description}</DialogDescription>
        </DialogHeader>

        <form action={handleSubmit} className="space-y-4">
          {/* Error Display */}
          {!state.success && state.error && (
            <Alert variant="destructive">
              <AlertDescription>{state.error}</AlertDescription>
            </Alert>
          )}

          {/* Success Display */}
          {state.success && state.data && (
            <Alert>
              <AlertDescription>Contact added successfully!</AlertDescription>
            </Alert>
          )}

          {/* Contact Search */}
          <div className="space-y-2">
            <Label htmlFor="contact-search">Contact</Label>
            <ContactSearchCombobox
              placeholder={t.searchPlaceholder}
              emptyMessage="No contacts found"
              minCharactersMessage="Type at least 2 characters to search"
              onSelect={(contactId) => setSelectedContactId(contactId)}
              name="contact_id"
            />
          </div>

          {/* Relationship Type */}
          <div className="space-y-2">
            <Label htmlFor="relationship-type">{t.relationshipType}</Label>
            <Select value={selectedRelationshipType} onValueChange={setSelectedRelationshipType}>
              <SelectTrigger>
                <SelectValue placeholder={t.selectRelationshipType} />
              </SelectTrigger>
              <SelectContent>
                {relationshipTypeOptions.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={handleClose} disabled={isPending}>
              {t.cancel}
            </Button>
            <Button
              type="submit"
              disabled={!selectedContactId || !selectedRelationshipType || isPending}
            >
              {isPending ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  {t.adding}
                </>
              ) : (
                t.addContact
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
