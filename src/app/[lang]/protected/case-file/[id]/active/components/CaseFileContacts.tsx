"use client";

import { useState, useActionState, useTransition } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Users, Mail, Phone, MapPin, Eye, Plus, Trash2 } from "lucide-react";
import { ContactDrawer } from "./ContactDrawer";
import { AddContactModal } from "./AddContactModal";
import { removeContactFromCaseFile } from "../actions/contact-management";
import { ActionState } from "@/lib/types/responses";
import { toast } from "sonner";

interface Contact {
  id: string;
  name: string;
  email?: any;
  phone?: any;
  address?: string;
  relationship_type: string;
}

interface CaseFileContactsProps {
  contacts: Contact[];
  caseFileId: string;
  lang: string;
  dictionary: {
    title: string;
    addContact: string;
    removeContact: string;
    viewContact: string;
    noContacts: string;
    noContactsDescription: string;
    contactAdded: string;
    contactRemoved: string;
    removeContactConfirm: string;
    addContactModal: {
      title: string;
      description: string;
      searchPlaceholder: string;
      relationshipType: string;
      selectRelationshipType: string;
      addContact: string;
      adding: string;
      cancel: string;
    };
    relationshipTypes: {
      parent: string;
      child: string;
      guardian: string;
      sibling: string;
      grandparent: string;
      other_family: string;
      social_worker: string;
      therapist: string;
      teacher: string;
      doctor: string;
      lawyer: string;
      other_professional: string;
      friend: string;
      neighbor: string;
      other: string;
    };
    contactInfo: {
      email: string;
      phone: string;
      address: string;
      personalEmail: string;
      workEmail: string;
      mobilePhone: string;
      homePhone: string;
      workPhone: string;
      noEmailsListed: string;
      noPhoneNumbers: string;
      noAddressProvided: string;
    };
    contactDrawer: {
      title: string;
      contactInformation: string;
      close: string;
    };
  };
}

/**
 * Case File Contacts component
 * Displays contacts in a grid with click to view details
 */
export function CaseFileContacts({
  contacts,
  caseFileId,
  lang,
  dictionary,
}: CaseFileContactsProps) {
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isPending, startTransition] = useTransition();

  // Remove contact action state
  const initialRemoveState: ActionState<any> = {
    success: true,
    error: "",
    data: null,
  };

  const [removeState, removeAction] = useActionState(removeContactFromCaseFile, initialRemoveState);

  // Extract email from JSONB field
  const getEmail = (contact: Contact) => {
    if (!contact.email) return null;
    if (typeof contact.email === "string") return contact.email;
    return contact.email.personal || contact.email.work || null;
  };

  // Extract phone from JSONB field
  const getPhone = (contact: Contact) => {
    if (!contact.phone) return null;
    if (typeof contact.phone === "string") return contact.phone;
    return contact.phone.mobile || contact.phone.home || null;
  };

  // Handle contact click
  const handleContactClick = (contact: Contact) => {
    setSelectedContact(contact);
    setIsDrawerOpen(true);
  };

  // Close drawer
  const closeDrawer = () => {
    setIsDrawerOpen(false);
    setSelectedContact(null);
  };

  // Handle remove contact
  const handleRemoveContact = (contactId: string, contactName: string) => {
    if (!confirm(dictionary.removeContactConfirm.replace("{name}", contactName))) {
      return;
    }

    startTransition(() => {
      const formData = new FormData();
      formData.append("caseFileId", caseFileId);
      formData.append("contactId", contactId);
      formData.append("lang", lang);

      removeAction(formData);

      // Show toast notification
      if (removeState.success && !removeState.error) {
        toast.success(dictionary.contactRemoved.replace("{name}", contactName));
      } else if (removeState.error) {
        toast.error(`Failed to remove contact: ${removeState.error}`);
      }
    });
  };

  if (!contacts || contacts.length === 0) {
    return (
      <>
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                {dictionary.title} (0)
              </CardTitle>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsAddModalOpen(true)}
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                {dictionary.addContact}
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8">
              <Users className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">{dictionary.noContacts}</h3>
              <p className="text-muted-foreground mb-4">{dictionary.noContactsDescription}</p>
            </div>
          </CardContent>
        </Card>
        {/* Add Contact Modal */}
        <AddContactModal
          isOpen={isAddModalOpen}
          onClose={() => setIsAddModalOpen(false)}
          caseFileId={caseFileId}
          lang={lang}
          dictionary={dictionary.addContactModal}
          relationshipTypes={dictionary.relationshipTypes}
        />
      </>
    );
  }

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              {dictionary.title} ({contacts.length})
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsAddModalOpen(true)}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              {dictionary.addContact}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {contacts.map((contact) => {
              const email = getEmail(contact);
              const phone = getPhone(contact);

              return (
                <Card
                  key={contact.id}
                  className="cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => handleContactClick(contact)}
                >
                  <CardContent className="p-4">
                    <div className="space-y-3">
                      {/* Header */}
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h4 className="font-medium text-sm">{contact.name}</h4>
                          <Badge variant="secondary" className="text-xs mt-1">
                            {contact.relationship_type}
                          </Badge>
                        </div>
                        <div className="flex gap-1">
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleContactClick(contact);
                            }}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 text-red-600 hover:text-red-700 hover:bg-red-50"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleRemoveContact(contact.id, contact.name);
                            }}
                            disabled={isPending}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>

                      {/* Contact Info */}
                      <div className="space-y-2 text-xs text-muted-foreground">
                        {email && (
                          <div className="flex items-center gap-2">
                            <Mail className="h-3 w-3" />
                            <span className="truncate">{email}</span>
                          </div>
                        )}
                        {phone && (
                          <div className="flex items-center gap-2">
                            <Phone className="h-3 w-3" />
                            <span>{phone}</span>
                          </div>
                        )}
                        {contact.address && (
                          <div className="flex items-center gap-2">
                            <MapPin className="h-3 w-3" />
                            <span className="truncate">{contact.address}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Contact Details Drawer */}
      <ContactDrawer isOpen={isDrawerOpen} onClose={closeDrawer} contact={selectedContact} />

      {/* Add Contact Modal */}
      <AddContactModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        caseFileId={caseFileId}
        lang={lang}
        dictionary={dictionary.addContactModal}
        relationshipTypes={dictionary.relationshipTypes}
      />
    </>
  );
}
