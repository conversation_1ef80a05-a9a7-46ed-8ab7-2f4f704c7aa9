import { Suspense } from "react";
import Link from "next/link";
import { notFound } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft } from "lucide-react";
import { viewObservationNote } from "../../../../../../observation-notes/actions/view";
import { getAvailableAppointments } from "../../../../../../observation-notes/actions/list";
import EditCaseFileObservationNoteForm from "./components/EditCaseFileObservationNoteForm";
import { getDictionary } from "@/lib/i18n/cache";

interface EditCaseFileObservationNotePageProps {
  params: {
    lang: string;
    id: string; // case file id
    noteId: string; // observation note id
  };
}

export default async function EditCaseFileObservationNotePage({
  params,
}: EditCaseFileObservationNotePageProps) {
  const { lang, id: caseFileId, noteId } = params;

  return (
    <div className="container mx-auto py-6 space-y-6">
      <Suspense fallback={<div>Loading form...</div>}>
        <EditCaseFileObservationNoteWrapper lang={lang} caseFileId={caseFileId} noteId={noteId} />
      </Suspense>
    </div>
  );
}

async function EditCaseFileObservationNoteWrapper({
  lang,
  caseFileId,
  noteId,
}: {
  lang: string;
  caseFileId: string;
  noteId: string;
}) {
  const dictionary = await getDictionary();
  const t = dictionary.observationNotes || {
    edit: {
      title: "Edit Observation Note",
      description: "Update the observation note details",
      backToNote: "Back to Note",
      formTitle: "Edit Observation Note",
      formDescription: "Update the details below to modify the observation note."
    }
  };

  // Get the observation note
  const noteResult = await viewObservationNote({ success: false, error: "", data: null }, { id: noteId });

  if (!noteResult.success || !noteResult.data) {
    if (noteResult.error?.includes("not found")) {
      notFound();
    }
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-destructive">Error loading note: {noteResult.error}</p>
          <Button variant="outline" asChild className="mt-4">
            <Link href={`/${lang}/protected/case-file/${caseFileId}/active?tab=observation-notes`}>
              Back to Case File
            </Link>
          </Button>
        </CardContent>
      </Card>
    );
  }

  // Get available appointments
  const appointmentsResult = await getAvailableAppointments();

  if (!appointmentsResult.success) {
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-destructive mb-4">
            Error loading appointments: {appointmentsResult.error}
          </p>
          <Button variant="outline" asChild>
            <Link href={`/${lang}/protected/case-file/${caseFileId}/active/observation-notes/${noteId}`}>
              {t.edit.backToNote}
            </Link>
          </Button>
        </CardContent>
      </Card>
    );
  }

  const note = noteResult.data;
  const appointments = appointmentsResult.data || [];

  return (
    <>
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="outline" size="sm" asChild>
          <Link href={`/${lang}/protected/case-file/${caseFileId}/active/observation-notes/${noteId}`}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t.edit.backToNote}
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold">{t.edit.title}</h1>
          <p className="text-muted-foreground">{t.edit.description}</p>
        </div>
      </div>

      {/* Form */}
      <Card>
        <CardHeader>
          <CardTitle>{t.edit.formTitle}</CardTitle>
          <CardDescription>{t.edit.formDescription}</CardDescription>
        </CardHeader>
        <CardContent>
          <EditCaseFileObservationNoteForm
            lang={lang}
            caseFileId={caseFileId}
            note={note}
            appointments={appointments}
          />
        </CardContent>
      </Card>
    </>
  );
}
