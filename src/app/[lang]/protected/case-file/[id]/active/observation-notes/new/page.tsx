import { Suspense } from "react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft } from "lucide-react";
import { getAvailableAppointments } from "../../../../../observation-notes/actions/list";
import CreateCaseFileObservationNoteForm from "./components/CreateCaseFileObservationNoteForm";
import { getDictionary } from "@/lib/i18n/cache";

interface CreateCaseFileObservationNotePageProps {
  params: {
    lang: string;
    id: string; // case file id
  };
}

export default async function CreateCaseFileObservationNotePage({
  params,
}: CreateCaseFileObservationNotePageProps) {
  const { lang, id: caseFileId } = params;
  const dictionary = await getDictionary();
  const t = dictionary.observationNotes || {
    create: {
      title: "Create Observation Note",
      description: "Create a new observation note for an appointment in this case file",
      backToCase: "Back to Case File",
      formTitle: "New Observation Note",
      formDescription: "Fill in the details below to create a new observation note for this case file."
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="outline" size="sm" asChild>
          <Link href={`/${lang}/protected/case-file/${caseFileId}/active?tab=observation-notes`}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t.create.backToCase}
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold">{t.create.title}</h1>
          <p className="text-muted-foreground">{t.create.description}</p>
        </div>
      </div>

      {/* Form */}
      <Card>
        <CardHeader>
          <CardTitle>{t.create.formTitle}</CardTitle>
          <CardDescription>{t.create.formDescription}</CardDescription>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<div>Loading form...</div>}>
            <CreateCaseFileObservationNoteFormWrapper lang={lang} caseFileId={caseFileId} />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  );
}

async function CreateCaseFileObservationNoteFormWrapper({ 
  lang, 
  caseFileId 
}: { 
  lang: string; 
  caseFileId: string; 
}) {
  const appointmentsResult = await getAvailableAppointments();

  if (!appointmentsResult.success) {
    return (
      <div className="text-center py-8">
        <p className="text-destructive mb-4">
          Error loading appointments: {appointmentsResult.error}
        </p>
        <Button variant="outline" asChild>
          <Link href={`/${lang}/protected/case-file/${caseFileId}/active?tab=observation-notes`}>
            Back to Case File
          </Link>
        </Button>
      </div>
    );
  }

  let appointments = appointmentsResult.data || [];

  // Filter appointments by case file ID
  // TODO: This should be done in the service, but for now we'll filter here
  // appointments = appointments.filter(apt => apt.case_file_id === caseFileId);

  if (appointments.length === 0) {
    return (
      <div className="text-center py-8">
        <h3 className="text-lg font-semibold mb-2">No Available Appointments</h3>
        <p className="text-muted-foreground mb-4">
          There are no appointments in this case file available for creating observation notes.
        </p>
        <Button variant="outline" asChild>
          <Link href={`/${lang}/protected/case-file/${caseFileId}/active?tab=observation-notes`}>
            Back to Case File
          </Link>
        </Button>
      </div>
    );
  }

  return <CreateCaseFileObservationNoteForm lang={lang} caseFileId={caseFileId} appointments={appointments} />;
}
