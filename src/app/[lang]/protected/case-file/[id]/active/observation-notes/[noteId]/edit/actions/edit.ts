"use server";

import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";
import { z } from "zod";
import { ObservationNotesService } from "../../../../../../../observation-notes/lib/services/ObservationNotesService";
import { ActionState } from "@/lib/types/responses";
import { logger } from "@/lib/logger/services/LoggerService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { DOMAIN_PERMISSIONS } from "../../../../../../../observation-notes/lib/config/domain";

// Validation schema
const editCaseFileObservationNoteSchema = z.object({
  id: z.string().uuid("Invalid observation note ID"),
  caseFileId: z.string().uuid("Invalid case file ID"),
  appointment_id: z.string().uuid("Invalid appointment ID"),
  title: z.string().min(1, "Title is required").max(255, "Title too long"),
  content: z.string().min(1, "Content is required"),
  status: z.enum(["draft", "pending_approval", "approved", "rejected"]).optional(),
});

/**
 * Update an observation note within a case file context
 */
export const editCaseFileObservationNote = requirePermission(DOMAIN_PERMISSIONS.UPDATE)(async (
  _prevState: ActionState<any>,
  formData: FormData
): Promise<ActionState<any>> => {
  const lang = formData.get("lang") as string;
  const caseFileId = formData.get("caseFileId") as string;
  const id = formData.get("id") as string;
  let redirectPath: string | null = null;

  try {
    // Extract and validate form data
    const rawData = {
      id,
      caseFileId,
      appointment_id: formData.get("appointment_id") as string,
      title: formData.get("title") as string,
      content: formData.get("content") as string,
      status: formData.get("status") as string,
    };

    const validationResult = editCaseFileObservationNoteSchema.safeParse(rawData);
    
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map((err) => err.message).join(", ");
      return {
        success: false,
        error: errors,
        data: null,
      };
    }

    const { appointment_id, title, content, status } = validationResult.data;

    // Update the observation note
    const result = await ObservationNotesService.update(id, {
      attached_to_id: appointment_id,
      attached_to_type: "appointment",
      title,
      content,
      status: status || "draft",
    });

    if (!result.success) {
      logger.error(`Error updating case file observation note: ${result.error}`);
      return {
        success: false,
        error: result.error || "Failed to update observation note",
        data: null,
      };
    }

    // Revalidate pages
    revalidatePath(`/${lang}/protected/case-file/${caseFileId}/active`);
    revalidatePath(`/${lang}/protected/case-file/${caseFileId}/active/observation-notes/${id}`);
    
    // Set redirect path to stay within case file context
    redirectPath = `/${lang}/protected/case-file/${caseFileId}/active/observation-notes/${id}`;

    return {
      success: true,
      error: "",
      data: result.data,
    };

  } catch (error) {
    logger.error(`Unexpected error updating case file observation note: ${error}`);
    return {
      success: false,
      error: `An unexpected error occurred: ${error}`,
      data: null,
    };
  } finally {
    // Redirect outside try-catch
    if (redirectPath) {
      redirect(redirectPath);
    }
  }
});
