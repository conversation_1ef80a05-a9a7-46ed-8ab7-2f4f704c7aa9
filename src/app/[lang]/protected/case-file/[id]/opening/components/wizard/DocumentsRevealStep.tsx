"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { H2, P } from "@/components/typography";
import { ContactList } from "../contacts/ContactList";
import { DocumentWorkflow } from "../documents/DocumentWorkflow";
import { ContactStatus } from "../../types/ContactStatus";
import { CaseFileWithRelations } from "../../../../lib/types";
import { Dictionary } from "@/lib/i18n/services/I18nService";

interface DocumentsRevealStepProps {
  caseFile: CaseFileWithRelations;
  lang: string;
  dictionary: Dictionary;
  contactStatuses: ContactStatus[];
}

/**
 * Documents reveal step component
 * Shows the complete interface with contacts and document workflow
 */
export function DocumentsRevealStep({
  caseFile,
  lang,
  dictionary,
  contactStatuses,
}: DocumentsRevealStepProps) {
  return (
    <div className="space-y-6">
      {/* Success Message */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
      >
        <Card className="bg-green-50 border-green-200">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="bg-green-100 p-2 rounded-full">
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.3, type: "spring" }}
                >
                  ✨
                </motion.div>
              </div>
              <div>
                <P className="font-medium text-green-800">
                  {dictionary.caseFileOpening.wizard.documents.successMessage}
                </P>
                <P className="text-sm text-green-700">
                  {dictionary.caseFileOpening.wizard.documents.successDescription}
                </P>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Main Interface */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Sidebar - Contact List */}
        <motion.div
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2, duration: 0.8, ease: "easeOut" }}
          className="lg:col-span-1"
        >
          <Card>
            <CardContent className="p-6">
              <div className="space-y-4">
                <H2>{dictionary.caseFileOpening.contacts.title}</H2>
                <P className="text-sm text-muted-foreground">
                  {dictionary.caseFileOpening.contacts.description}
                </P>

                <ContactList
                  contacts={caseFile.contacts || []}
                  caseFileId={caseFile.id}
                  lang={lang}
                  dictionary={dictionary}
                />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Right Main Content - Document Workflow */}
        <motion.div
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.4, duration: 0.8, ease: "easeOut" }}
          className="lg:col-span-2"
        >
          <DocumentWorkflow
            caseFileId={caseFile.id}
            dictionary={dictionary}
            contactStatuses={contactStatuses}
          />
        </motion.div>
      </div>
    </div>
  );
}
