"use client";

import { useState, useActionState } from "react";
import { useRouter } from "next/navigation";
import { motion, AnimatePresence } from "framer-motion";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { H3, P } from "@/components/typography";
import { Play, AlertCircle, CheckCircle, Loader2 } from "lucide-react";
import { activateCaseFile } from "../../../../actions/active-state";
import { ActionState } from "@/lib/types/responses";
import { Dictionary } from "@/lib/i18n/services/I18nService";

interface ActivationFormProps {
  caseFileId: string;
  onClose: () => void;
  dictionary: Dictionary;
}

/**
 * Case File Activation Form
 * Modal form for activating a case file with optional notes
 */
export function ActivationForm({
  caseFileId,
  onClose,
  dictionary: _dictionary,
}: ActivationFormProps) {
  const router = useRouter();
  const [notes, setNotes] = useState("");
  const [showConfirmation, setShowConfirmation] = useState(false);

  // Initial state for the form
  const initialState: ActionState<{ id: string } | null> = {
    success: true,
    error: "",
    data: null,
  };

  // Use useActionState for server action integration
  const [state, formAction, pending] = useActionState(
    async (_prevState: ActionState<{ id: string } | null>, formData: FormData) => {
      const result = await activateCaseFile(caseFileId, formData);

      if (result.success) {
        // Show success state briefly, then redirect
        setTimeout(() => {
          router.push(window.location.pathname.replace("/opening", "/active"));
        }, 1500);
      }

      return result;
    },
    initialState
  );

  const handleSubmit = () => {
    setShowConfirmation(true);
  };

  const handleConfirm = () => {
    const formData = new FormData();
    if (notes.trim()) {
      formData.append("notes", notes.trim());
    }
    formAction(formData);
  };

  // Show success state
  if (state.success && state.data) {
    return (
      <Dialog open={true} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-md">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="text-center space-y-4 py-6"
          >
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
              className="flex justify-center"
            >
              <div className="bg-green-100 p-3 rounded-full">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
            </motion.div>

            <div className="space-y-2">
              <H3>Case File Activated!</H3>
              <P className="text-muted-foreground">
                The case file has been successfully activated and is now ready for active service
                delivery.
              </P>
            </div>

            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5 }}
              className="text-sm text-muted-foreground"
            >
              Redirecting to active case file...
            </motion.div>
          </motion.div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Play className="h-5 w-5" />
            Activate Case File
          </DialogTitle>
          <DialogDescription>
            This will transition the case file from opening to active status, enabling full service
            delivery.
          </DialogDescription>
        </DialogHeader>

        <AnimatePresence mode="wait">
          {!showConfirmation ? (
            <motion.div
              key="form"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              className="space-y-4"
            >
              {/* Error Display */}
              {!state.success && state.error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{state.error}</AlertDescription>
                </Alert>
              )}

              {/* Notes Field */}
              <div className="space-y-2">
                <Label htmlFor="notes">Activation Notes (Optional)</Label>
                <Textarea
                  id="notes"
                  placeholder="Add any notes about the activation..."
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  rows={3}
                />
                <P className="text-sm text-muted-foreground">
                  These notes will be recorded in the case file history.
                </P>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end gap-3 pt-4">
                <Button variant="outline" onClick={onClose}>
                  Cancel
                </Button>
                <Button onClick={handleSubmit}>Continue</Button>
              </div>
            </motion.div>
          ) : (
            <motion.div
              key="confirmation"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-4"
            >
              {/* Confirmation Message */}
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Confirm Activation:</strong> This action will change the case file status
                  to active and enable full service delivery capabilities. This action cannot be
                  undone.
                </AlertDescription>
              </Alert>

              {/* Summary */}
              <div className="space-y-2">
                <P className="font-medium">Activation Summary:</P>
                <ul className="text-sm text-muted-foreground space-y-1 ml-4">
                  <li>• Case file status will change to "Active"</li>
                  <li>• Full dashboard and service tools will be enabled</li>
                  <li>• Scheduling and appointment management will be available</li>
                  <li>• Progress tracking and reporting will begin</li>
                </ul>
              </div>

              {notes.trim() && (
                <div className="space-y-2">
                  <P className="font-medium">Notes:</P>
                  <div className="p-3 bg-muted rounded-md">
                    <P className="text-sm">{notes}</P>
                  </div>
                </div>
              )}

              {/* Confirmation Buttons */}
              <div className="flex justify-end gap-3 pt-4">
                <Button
                  variant="outline"
                  onClick={() => setShowConfirmation(false)}
                  disabled={pending}
                >
                  Back
                </Button>
                <Button
                  onClick={handleConfirm}
                  disabled={pending}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {pending ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Activating...
                    </>
                  ) : (
                    <>
                      <Play className="h-4 w-4 mr-2" />
                      Activate Case File
                    </>
                  )}
                </Button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </DialogContent>
    </Dialog>
  );
}
