"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { H3, P } from "@/components/typography";
import { CheckCircle, X, Users, Play, AlertCircle, Target } from "lucide-react";
import { ActivationForm } from "./ActivationForm";
import { Dictionary } from "@/lib/i18n/services/I18nService";
import { ContactStatus } from "../../types/ContactStatus";

interface CaseFileActivationPanelProps {
  caseFileId: string;
  dictionary: Dictionary;
  contactStatuses: ContactStatus[];
}

/**
 * Case File Activation Panel
 * Shows document completion status by contact and activation controls
 */
export function CaseFileActivationPanel({
  caseFileId,
  dictionary,
  contactStatuses,
}: CaseFileActivationPanelProps) {
  const [showActivationForm, setShowActivationForm] = useState(false);

  const allContactsComplete = contactStatuses.every((status) => status.isComplete);
  const incompleteContacts = contactStatuses.filter((status) => !status.isComplete);

  const getContactIcon = (status: ContactStatus) => {
    if (status.isComplete) {
      return <CheckCircle className="h-5 w-5 text-green-500" />;
    }
    return <X className="h-5 w-5 text-red-500" />;
  };

  const getContactBadge = (status: ContactStatus) => {
    if (status.isComplete) {
      return (
        <Badge variant="default" className="bg-green-100 text-green-800">
          Complete
        </Badge>
      );
    }
    return <Badge variant="destructive">Incomplete</Badge>;
  };

  return (
    <>
      <Card className="h-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Case File Activation
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Contact Document Status */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            className="space-y-4"
          >
            <H3>Document Status by Contact</H3>

            {contactStatuses.length === 0 ? (
              <div className="p-4 rounded-lg bg-muted/50 text-center">
                <Users className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                <P className="text-muted-foreground">No contacts found for this case file</P>
              </div>
            ) : (
              <div className="space-y-3">
                {contactStatuses.map((status: ContactStatus, index: number) => (
                  <motion.div
                    key={status.contactId}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1, duration: 0.5 }}
                    className="flex items-start gap-3 p-3 rounded-lg border bg-card"
                  >
                    {getContactIcon(status)}
                    <div className="flex-1 space-y-1">
                      <div className="flex items-center justify-between">
                        <P className="font-medium">{status.contactName}</P>
                        {getContactBadge(status)}
                      </div>
                      <P className="text-sm text-muted-foreground">{status.reason}</P>
                    </div>
                  </motion.div>
                ))}
              </div>
            )}
          </motion.div>

          {/* Status Summary */}
          {contactStatuses.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.6, ease: "easeOut" }}
              className="p-4 rounded-lg bg-muted/50 space-y-2"
            >
              <div className="flex items-center gap-2">
                {allContactsComplete ? (
                  <CheckCircle className="h-5 w-5 text-green-500" />
                ) : (
                  <AlertCircle className="h-5 w-5 text-orange-500" />
                )}
                <P className="font-medium">
                  {allContactsComplete ? "Ready for Activation" : "Documents Incomplete"}
                </P>
              </div>
              <P className="text-sm text-muted-foreground">
                {allContactsComplete
                  ? "All contacts have completed their required documents. Case file can be activated."
                  : `${incompleteContacts.length} contact(s) have incomplete documents. Complete all documents before activation.`}
              </P>
            </motion.div>
          )}

          {/* Activation Button */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.6, ease: "easeOut" }}
            className="pt-4"
          >
            <Button
              onClick={() => setShowActivationForm(true)}
              disabled={!allContactsComplete || contactStatuses.length === 0}
              className="w-full"
              size="lg"
            >
              <Play className="h-4 w-4 mr-2" />
              {allContactsComplete && contactStatuses.length > 0
                ? "Activate Case File"
                : "Complete All Documents First"}
            </Button>

            {(!allContactsComplete || contactStatuses.length === 0) && (
              <div className="flex items-center gap-2 mt-2 text-sm text-muted-foreground">
                <AlertCircle className="h-4 w-4" />
                <span>
                  {contactStatuses.length === 0
                    ? "Add contacts before activation"
                    : "All contact documents must be completed before activation"}
                </span>
              </div>
            )}
          </motion.div>
        </CardContent>
      </Card>

      {/* Activation Form Modal */}
      {showActivationForm && (
        <ActivationForm
          caseFileId={caseFileId}
          onClose={() => setShowActivationForm(false)}
          dictionary={dictionary}
        />
      )}
    </>
  );
}
