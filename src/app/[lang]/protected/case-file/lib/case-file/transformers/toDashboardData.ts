import type { CaseFileDashboardData } from "../../types";
import type { DashboardData } from "../queries/getCaseFileDashboardData";

/**
 * Transform domain dashboard data to dashboard data format
 * @param domainData Dashboard data from domain layer
 * @returns Dashboard data format
 */
export function toDashboardData(domainData: DashboardData): CaseFileDashboardData {
  return {
    caseFile: domainData.caseFile, // Use the domain case file directly (CaseFileWithDetails)
    familyInfo: domainData.familyInfo,
    serviceRequirements: domainData.serviceRequirements,
    recentActivity: domainData.recentActivity.map((activity) => ({
      ...activity,
      type: activity.type as "appointment" | "document" | "note" | "status_change",
    })),
  };
}
