import { AppointmentStatus } from "../types";

/**
 * Appointment domain configuration
 */
export const APPOINTMENT_CONFIG = {
  // Domain information
  domain: "appointments",
  name: "Appointment Management",
  description: "Self-contained appointment scheduling system",

  // Pagination defaults
  pagination: {
    defaultLimit: 10,
    maxLimit: 100,
  },

  // Status configuration
  statuses: {
    planned: {
      label: "Planned",
      color: "blue",
      description: "Appointment is scheduled but not confirmed",
    },
    confirmed: {
      label: "Confirmed",
      color: "green",
      description: "Appointment is confirmed by all parties",
    },
    in_progress: {
      label: "In Progress",
      color: "yellow",
      description: "Appointment is currently happening",
    },
    completed: {
      label: "Completed",
      color: "emerald",
      description: "Appointment has been completed successfully",
    },
    missed: {
      label: "Missed",
      color: "orange",
      description: "Appointment was missed by attendee",
    },
    postponed: {
      label: "Postponed",
      color: "purple",
      description: "Appointment has been postponed to a later date",
    },
    cancelled: {
      label: "Cancelled",
      color: "red",
      description: "Appointment has been cancelled",
    },
  } as const,

  // Status transitions - what statuses can change to what
  statusTransitions: {
    planned: ["confirmed", "postponed", "cancelled"],
    confirmed: ["in_progress", "postponed", "cancelled", "missed"],
    in_progress: ["completed", "cancelled"],
    completed: [], // Final state
    missed: ["planned"], // Can reschedule
    postponed: ["planned", "cancelled"],
    cancelled: [], // Final state
  } as Record<AppointmentStatus, AppointmentStatus[]>,

  // Time configuration
  time: {
    defaultDuration: 60, // minutes
    minDuration: 15,
    maxDuration: 480, // 8 hours
    workingHours: {
      start: "08:00",
      end: "18:00",
    },
  },

  // Validation rules
  validation: {
    title: {
      minLength: 3,
      maxLength: 100,
    },
    description: {
      maxLength: 500,
    },
    futureBooking: {
      minDaysAhead: 0, // Can book same day
      maxDaysAhead: 365, // Can book up to 1 year ahead
    },
  },
} as const;

/**
 * Get status configuration
 */
export function getStatusConfig(status: AppointmentStatus) {
  return APPOINTMENT_CONFIG.statuses[status];
}

/**
 * Check if status transition is allowed
 */
export function isStatusTransitionAllowed(from: AppointmentStatus, to: AppointmentStatus): boolean {
  return APPOINTMENT_CONFIG.statusTransitions[from]?.includes(to) ?? false;
}

/**
 * Get allowed status transitions for a given status
 */
export function getAllowedStatusTransitions(status: AppointmentStatus): AppointmentStatus[] {
  return APPOINTMENT_CONFIG.statusTransitions[status] ?? [];
}
