import { Suspense } from "react";
import { listAppointments } from "../../actions/list";
import { AppointmentTable } from "../../components/AppointmentTable";
import { AppointmentFilters } from "../../components/AppointmentFilters";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Calendar, List } from "lucide-react";
import Link from "next/link";
import { getDictionary } from "@/lib/i18n/services/I18nService";

interface AppointmentListPageProps {
  params: Promise<{
    lang: string;
  }>;
  searchParams: Promise<{
    status?: string;
    date?: string;
    search?: string;
    page?: string;
  }>;
}

/**
 * Appointment List Page
 * Displays all appointments in a table format with filtering options
 */
export default async function AppointmentListPage({
  params,
  searchParams: promiseSearchParams,
}: AppointmentListPageProps) {
  const { lang } = await params;
  const dictionary = await getDictionary(lang);
  const searchParams = await promiseSearchParams;
  const result = await listAppointments({
    status: searchParams.status as any,
    date: searchParams.date,
    search: searchParams.search,
    page: parseInt(searchParams.page || "1"),
  });

  if (!result.success) {
    throw new Error(result.message);
  }

  if (!result.data) {
    throw new Error("No data returned");
  }

  const { appointments, pagination } = result.data;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{dictionary.appointments.title}</h1>
          <p className="text-muted-foreground">{dictionary.appointments.description}</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" asChild>
            <Link href="./calendar">
              <Calendar className="h-4 w-4 mr-2" />
              {dictionary.appointments.calendarView}
            </Link>
          </Button>
          <Button asChild>
            <Link href="./new">
              <Calendar className="h-4 w-4 mr-2" />
              {dictionary.appointments.newAppointment}
            </Link>
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <List className="h-5 w-5" />
            {dictionary.appointments.filterAppointments}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <AppointmentFilters dictionary={dictionary} />
        </CardContent>
      </Card>

      {/* Appointments Table */}
      <Card>
        <CardContent className="p-0">
          <Suspense
            fallback={<div className="p-6">{dictionary.appointments.loadingAppointments}</div>}
          >
            <AppointmentTable
              appointments={appointments}
              pagination={pagination}
              dictionary={dictionary}
            />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  );
}
