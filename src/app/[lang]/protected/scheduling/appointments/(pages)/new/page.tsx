import { Suspense } from "react";
import { AppointmentForm } from "../../components/AppointmentForm";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { getDictionary } from "@/lib/i18n/services/I18nService";

interface NewAppointmentPageProps {
  params: Promise<{
    lang: string;
  }>;
}

/**
 * New Appointment Page
 * Dedicated page for creating new appointments
 */
export default async function NewAppointmentPage({ params }: NewAppointmentPageProps) {
  const { lang } = await params;
  const dictionary = await getDictionary(lang);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {dictionary.appointments.createAppointment}
          </h1>
          <p className="text-muted-foreground">{dictionary.appointments.scheduleAppointment}</p>
        </div>
        <Button variant="outline" asChild>
          <Link href="./list">
            <ArrowLeft className="h-4 w-4 mr-2" />
            {dictionary.appointments.backToAppointments}
          </Link>
        </Button>
      </div>

      {/* Form Card */}
      <Card>
        <CardHeader>
          <CardTitle>{dictionary.appointments.appointmentDetails}</CardTitle>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<div>{dictionary.appointments.loadingForm}</div>}>
            <AppointmentForm mode="create" dictionary={dictionary} />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  );
}
