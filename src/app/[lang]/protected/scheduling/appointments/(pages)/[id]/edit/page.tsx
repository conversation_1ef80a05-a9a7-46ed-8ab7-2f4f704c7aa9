import { Suspense } from "react";
import { notFound } from "next/navigation";
import { viewAppointment } from "../../../actions/view";
import { AppointmentForm } from "../../../components/AppointmentForm";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Edit } from "lucide-react";
import Link from "next/link";
import { getDictionary } from "@/lib/i18n/services/I18nService";

interface AppointmentEditPageProps {
  params: Promise<{
    lang: string;
    id: string;
  }>;
}

/**
 * Appointment Edit Page
 * Allows editing of an existing appointment
 */
export default async function AppointmentEditPage({ params }: AppointmentEditPageProps) {
  const { lang, id } = await params;
  const dictionary = await getDictionary(lang);
  const result = await viewAppointment(id);

  if (!result.success) {
    if (result.message.includes("not found")) {
      notFound();
    }
    throw new Error(result.message);
  }

  const appointment = result.data;

  if (!appointment) {
    notFound();
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="./view">
              <ArrowLeft className="h-4 w-4 mr-2" />
              {dictionary.appointments.backToDetails}
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              {dictionary.appointments.editAppointment}
            </h1>
            <p className="text-muted-foreground">
              {dictionary.appointments.updateAppointmentInformation}
            </p>
          </div>
        </div>
      </div>

      {/* Edit Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Edit className="h-5 w-5" />
            {dictionary.appointments.appointmentDetails}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<div className="p-6">{dictionary.appointments.loadingForm}</div>}>
            <AppointmentForm appointment={appointment} mode="edit" dictionary={dictionary} />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  );
}
