import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card";
import { Calendar, ArrowLeft } from "lucide-react";
import Link from "next/link";

/**
 * Not found page for appointment calendar
 */
export default function AppointmentCalendarNotFound() {
  return (
    <div className="flex items-center justify-center min-h-[400px]">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-muted flex items-center justify-center">
            <Calendar className="h-6 w-6 text-muted-foreground" />
          </div>
          <CardTitle>Calendar Not Found</CardTitle>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <p className="text-muted-foreground">
            The appointment calendar could not be found or you don't have permission to view it.
          </p>
          <Button asChild className="w-full">
            <Link href="/protected/scheduling">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Scheduling
            </Link>
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
