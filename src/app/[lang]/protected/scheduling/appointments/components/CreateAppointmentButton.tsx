"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Modal } from "@/components/ui/modal";
import { Plus } from "lucide-react";
import { AppointmentForm } from "./AppointmentForm";
import { Dictionary } from "@/lib/i18n/services/I18nService";

interface CreateAppointmentButtonProps {
  variant?: "default" | "outline" | "secondary" | "ghost" | "link" | "destructive";
  size?: "default" | "sm" | "lg" | "icon";
  className?: string;
  dictionary: Dictionary;
}

/**
 * Button component for creating new appointments
 * Opens a modal with the appointment form
 */
export function CreateAppointmentButton({
  variant = "default",
  size = "default",
  className,
  dictionary,
}: CreateAppointmentButtonProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <>
      <Button
        variant={variant}
        size={size}
        className={className}
        onClick={() => setIsModalOpen(true)}
      >
        <Plus className="h-4 w-4 mr-2" />
        {dictionary.appointments.newAppointment}
      </Button>

      <Modal
        open={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title={dictionary.appointments.createAppointment}
        description={dictionary.appointments.scheduleAppointment}
      >
        <AppointmentForm
          mode="create"
          onSuccess={() => setIsModalOpen(false)}
          onCancel={() => setIsModalOpen(false)}
          dictionary={dictionary}
        />
      </Modal>
    </>
  );
}
