"use client";

import { useState } from "react";
import { ScheduleCalendar } from "@/components/ui/scheduling/schedule-calendar";
import { Button } from "@/components/ui/button";
import { Modal } from "@/components/ui/modal";
import { AppointmentWithDetails, AppointmentCalendarEvent } from "../lib/types";
import { AppointmentDetail } from "./AppointmentDetail";
import { AppointmentStatusActions } from "./AppointmentStatusActions";
import { Dictionary } from "@/lib/i18n/services/I18nService";

interface AppointmentCalendarProps {
  appointments: AppointmentWithDetails[];
  dictionary: Dictionary;
}

/**
 * Calendar component for displaying appointments
 * Uses the ScheduleCalendar component from the design system
 */
export function AppointmentCalendar({ appointments, dictionary }: AppointmentCalendarProps) {
  const [selectedAppointment, setSelectedAppointment] = useState<AppointmentWithDetails | null>(
    null
  );
  const [showDetailModal, setShowDetailModal] = useState(false);

  // Transform appointments to calendar events
  const calendarEvents: AppointmentCalendarEvent[] = appointments.map((appointment) => ({
    id: appointment.id,
    title: appointment.title,
    start: new Date(`${appointment.appointment_date}T${appointment.start_time}`),
    end: new Date(`${appointment.appointment_date}T${appointment.end_time}`),
    type: appointment.status,
    description: appointment.description || undefined,
  }));

  const handleEventClick = (event: any) => {
    const appointment = appointments.find((apt) => apt.id === event.id);
    if (appointment) {
      setSelectedAppointment(appointment);
      setShowDetailModal(true);
    }
  };

  const handleCloseModal = () => {
    setShowDetailModal(false);
    setSelectedAppointment(null);
  };

  return (
    <>
      <div className="space-y-4">
        {/* Calendar Instructions */}
        <div className="text-sm text-muted-foreground">
          Click on any appointment to view details. Use the calendar controls to navigate between
          months.
        </div>

        {/* Calendar Component */}
        <ScheduleCalendar
          events={calendarEvents}
          onEventClick={handleEventClick}
          className="w-full"
        />

        {/* Summary Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t">
          <div className="text-center">
            <p className="text-2xl font-bold">{appointments.length}</p>
            <p className="text-sm text-muted-foreground">Total {dictionary.appointments.title}</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-green-600">
              {appointments.filter((apt) => apt.status === "confirmed").length}
            </p>
            <p className="text-sm text-muted-foreground">
              {dictionary.appointments.status.confirmed}
            </p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-blue-600">
              {appointments.filter((apt) => apt.status === "planned").length}
            </p>
            <p className="text-sm text-muted-foreground">
              {dictionary.appointments.status.planned}
            </p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-emerald-600">
              {appointments.filter((apt) => apt.status === "completed").length}
            </p>
            <p className="text-sm text-muted-foreground">
              {dictionary.appointments.status.completed}
            </p>
          </div>
        </div>
      </div>

      {/* Appointment Detail Modal */}
      {selectedAppointment && (
        <Modal
          open={showDetailModal}
          onClose={handleCloseModal}
          title={selectedAppointment.title}
          description={`${selectedAppointment.date_display} at ${selectedAppointment.time_display}`}
          footer={
            <div className="flex justify-between items-center w-full">
              <AppointmentStatusActions appointment={selectedAppointment} dictionary={dictionary} />
              <div className="flex gap-2">
                <Button variant="outline" onClick={handleCloseModal}>
                  {dictionary.appointments.close}
                </Button>
                <Button asChild>
                  <a href={`${selectedAppointment.id}/view`}>
                    {dictionary.appointments.viewFullDetails}
                  </a>
                </Button>
              </div>
            </div>
          }
        >
          <AppointmentDetail appointment={selectedAppointment} dictionary={dictionary} />
        </Modal>
      )}
    </>
  );
}
