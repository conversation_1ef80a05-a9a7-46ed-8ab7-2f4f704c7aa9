import { Badge } from "@/components/ui/badge";
import { AppointmentStatus } from "../lib/types";
import { APPOINTMENT_CONFIG } from "../lib/config";
import { cn } from "@/lib/utils";
import { Dictionary } from "@/lib/i18n/services/I18nService";

interface AppointmentStatusBadgeProps {
  status: AppointmentStatus;
  className?: string;
  dictionary: Dictionary;
}

/**
 * Status badge component for appointments
 * Displays appointment status with appropriate colors
 */
export function AppointmentStatusBadge({
  status,
  className,
  dictionary,
}: AppointmentStatusBadgeProps) {
  const statusConfig = APPOINTMENT_CONFIG.statuses[status];

  if (!statusConfig) {
    return (
      <Badge variant="secondary" className={className}>
        {dictionary.appointments.status[status as keyof typeof dictionary.appointments.status] ||
          status}
      </Badge>
    );
  }

  // Map status colors to badge variants and custom classes
  const getStatusStyles = (color: string) => {
    switch (color) {
      case "blue":
        return {
          variant: "default" as const,
          className:
            "bg-blue-100 text-blue-800 hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-300",
        };
      case "green":
        return {
          variant: "default" as const,
          className:
            "bg-green-100 text-green-800 hover:bg-green-200 dark:bg-green-900 dark:text-green-300",
        };
      case "yellow":
        return {
          variant: "default" as const,
          className:
            "bg-yellow-100 text-yellow-800 hover:bg-yellow-200 dark:bg-yellow-900 dark:text-yellow-300",
        };
      case "emerald":
        return {
          variant: "default" as const,
          className:
            "bg-emerald-100 text-emerald-800 hover:bg-emerald-200 dark:bg-emerald-900 dark:text-emerald-300",
        };
      case "orange":
        return {
          variant: "default" as const,
          className:
            "bg-orange-100 text-orange-800 hover:bg-orange-200 dark:bg-orange-900 dark:text-orange-300",
        };
      case "purple":
        return {
          variant: "default" as const,
          className:
            "bg-purple-100 text-purple-800 hover:bg-purple-200 dark:bg-purple-900 dark:text-purple-300",
        };
      case "red":
        return {
          variant: "destructive" as const,
          className: "",
        };
      default:
        return {
          variant: "secondary" as const,
          className: "",
        };
    }
  };

  const styles = getStatusStyles(statusConfig.color);

  return (
    <Badge
      variant={styles.variant}
      className={cn(styles.className, className)}
      title={
        dictionary.appointments.statusDescriptions[
          status as keyof typeof dictionary.appointments.statusDescriptions
        ] || statusConfig.description
      }
    >
      {dictionary.appointments.status[status as keyof typeof dictionary.appointments.status] ||
        statusConfig.label}
    </Badge>
  );
}
