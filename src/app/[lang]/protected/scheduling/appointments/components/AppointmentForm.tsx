"use client";

import { useState, useTransition } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, AlertCircle } from "lucide-react";
import { createAppointmentAndReturnToList } from "../actions/create";
import { updateAppointment } from "../actions/update";
import { AppointmentWithDetails, AppointmentStatus } from "../lib/types";
import { APPOINTMENT_CONFIG } from "../lib/config";
import { Dictionary } from "@/lib/i18n/services/I18nService";

interface AppointmentFormProps {
  appointment?: AppointmentWithDetails;
  mode: "create" | "edit";
  onSuccess?: () => void;
  onCancel?: () => void;
  dictionary: Dictionary;
}

/**
 * Form component for creating and editing appointments
 */
export function AppointmentForm({
  appointment,
  mode,
  onSuccess,
  onCancel,
  dictionary,
}: AppointmentFormProps) {
  const [isPending, startTransition] = useTransition();
  const [error, setError] = useState<string | null>(null);

  // Form state
  const [formData, setFormData] = useState({
    title: appointment?.title || "",
    description: appointment?.description || "",
    appointment_date: appointment?.appointment_date || "",
    start_time: appointment?.start_time || "",
    end_time: appointment?.end_time || "",
    status: appointment?.status || ("planned" as AppointmentStatus),
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    const formDataObj = new FormData();
    Object.entries(formData).forEach(([key, value]) => {
      if (value) formDataObj.append(key, value);
    });

    startTransition(async () => {
      try {
        if (mode === "create") {
          const result = await createAppointmentAndReturnToList(formDataObj);
          if (result.success) {
            onSuccess?.();
          } else {
            setError(result.error || "Failed to create appointment");
          }
        } else if (mode === "edit" && appointment) {
          await updateAppointment(appointment.id, formDataObj);
          // updateAppointment redirects on success, so we won't reach here
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "An error occurred");
      }
    });
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    setError(null); // Clear error when user starts typing
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Title */}
      <div className="space-y-2">
        <Label htmlFor="title">{dictionary.appointments.form.title} *</Label>
        <Input
          id="title"
          value={formData.title}
          onChange={(e) => handleInputChange("title", e.target.value)}
          placeholder={dictionary.appointments.form.titlePlaceholder}
          required
          disabled={isPending}
        />
      </div>

      {/* Description */}
      <div className="space-y-2">
        <Label htmlFor="description">{dictionary.appointments.form.description}</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => handleInputChange("description", e.target.value)}
          placeholder={dictionary.appointments.form.descriptionPlaceholder}
          rows={3}
          disabled={isPending}
        />
      </div>

      {/* Date and Time */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="space-y-2">
          <Label htmlFor="appointment_date">{dictionary.appointments.form.date} *</Label>
          <Input
            id="appointment_date"
            type="date"
            value={formData.appointment_date}
            onChange={(e) => handleInputChange("appointment_date", e.target.value)}
            required
            disabled={isPending}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="start_time">{dictionary.appointments.form.startTime} *</Label>
          <Input
            id="start_time"
            type="time"
            value={formData.start_time}
            onChange={(e) => handleInputChange("start_time", e.target.value)}
            required
            disabled={isPending}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="end_time">{dictionary.appointments.form.endTime} *</Label>
          <Input
            id="end_time"
            type="time"
            value={formData.end_time}
            onChange={(e) => handleInputChange("end_time", e.target.value)}
            required
            disabled={isPending}
          />
        </div>
      </div>

      {/* Status */}
      <div className="space-y-2">
        <Label htmlFor="status">{dictionary.appointments.form.status}</Label>
        <Select
          value={formData.status}
          onValueChange={(value) => handleInputChange("status", value)}
          disabled={isPending}
        >
          <SelectTrigger id="status">
            <SelectValue placeholder={dictionary.appointments.form.selectStatus} />
          </SelectTrigger>
          <SelectContent>
            {Object.entries(APPOINTMENT_CONFIG.statuses).map(([key, config]) => (
              <SelectItem key={key} value={key}>
                {dictionary.appointments.status[
                  key as keyof typeof dictionary.appointments.status
                ] || config.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end gap-2 pt-4">
        {onCancel && (
          <Button type="button" variant="outline" onClick={onCancel} disabled={isPending}>
            {dictionary.appointments.form.cancel}
          </Button>
        )}
        <Button type="submit" disabled={isPending}>
          {isPending && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
          {mode === "create"
            ? isPending
              ? dictionary.appointments.form.creating
              : dictionary.appointments.form.createAppointment
            : isPending
              ? dictionary.appointments.form.updating
              : dictionary.appointments.form.updateAppointment}
        </Button>
      </div>
    </form>
  );
}
