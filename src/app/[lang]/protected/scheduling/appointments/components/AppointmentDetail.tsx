import { AppointmentWithDetails } from "../lib/types";
import { AppointmentStatusBadge } from "./AppointmentStatusBadge";
import { Badge } from "@/components/ui/badge";
import { Calendar, Clock, FileText, User } from "lucide-react";
import { Dictionary } from "@/lib/i18n/services/I18nService";

interface AppointmentDetailProps {
  appointment: AppointmentWithDetails;
  dictionary: Dictionary;
}

/**
 * Detailed view component for appointments
 * Displays all appointment information in a structured layout
 */
export function AppointmentDetail({ appointment, dictionary }: AppointmentDetailProps) {
  return (
    <div className="space-y-6">
      {/* Header Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-semibold mb-2">
              {dictionary.appointments.basicInformation}
            </h3>
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <FileText className="h-5 w-5 text-muted-foreground mt-0.5" />
                <div>
                  <p className="font-medium">{appointment.title}</p>
                  {appointment.description && (
                    <p className="text-sm text-muted-foreground mt-1">{appointment.description}</p>
                  )}
                </div>
              </div>

              <div className="flex items-center gap-3">
                <User className="h-5 w-5 text-muted-foreground" />
                <div>
                  <p className="text-sm text-muted-foreground">
                    {dictionary.appointments.form.status}
                  </p>
                  <AppointmentStatusBadge status={appointment.status} dictionary={dictionary} />
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-semibold mb-2">{dictionary.appointments.schedule}</h3>
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <Calendar className="h-5 w-5 text-muted-foreground" />
                <div>
                  <p className="text-sm text-muted-foreground">
                    {dictionary.appointments.form.date}
                  </p>
                  <p className="font-medium">{appointment.date_display}</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Clock className="h-5 w-5 text-muted-foreground" />
                <div>
                  <p className="text-sm text-muted-foreground">Time</p>
                  <p className="font-medium">{appointment.time_display}</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Clock className="h-5 w-5 text-muted-foreground" />
                <div>
                  <p className="text-sm text-muted-foreground">Duration</p>
                  <p className="font-medium">{appointment.duration_display}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Timestamps */}
      <div className="border-t pt-6">
        <h3 className="text-lg font-semibold mb-4">{dictionary.appointments.timeline}</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="space-y-1">
            <p className="text-sm text-muted-foreground">{dictionary.common.created}</p>
            <p className="text-sm font-medium">
              {appointment.created_at ? new Date(appointment.created_at).toLocaleString() : "N/A"}
            </p>
          </div>

          {appointment.updated_at && appointment.updated_at !== appointment.created_at && (
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">{dictionary.common.lastUpdated}</p>
              <p className="text-sm font-medium">
                {new Date(appointment.updated_at).toLocaleString()}
              </p>
            </div>
          )}

          {appointment.confirmed_at && (
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">
                {dictionary.appointments.status.confirmed}
              </p>
              <p className="text-sm font-medium">
                {new Date(appointment.confirmed_at).toLocaleString()}
              </p>
            </div>
          )}

          {appointment.completed_at && (
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">
                {dictionary.appointments.status.completed}
              </p>
              <p className="text-sm font-medium">
                {new Date(appointment.completed_at).toLocaleString()}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* System Information */}
      <div className="border-t pt-6">
        <h3 className="text-lg font-semibold mb-4">{dictionary.appointments.systemInformation}</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-1">
            <p className="text-sm text-muted-foreground">{dictionary.appointments.appointmentId}</p>
            <p className="text-sm font-mono bg-muted px-2 py-1 rounded">{appointment.id}</p>
          </div>

          <div className="space-y-1">
            <p className="text-sm text-muted-foreground">
              {dictionary.appointments.organizationId}
            </p>
            <p className="text-sm font-mono bg-muted px-2 py-1 rounded">
              {appointment.organization_id}
            </p>
          </div>
        </div>
      </div>

      {/* Integration Status */}
      <div className="border-t pt-6">
        <h3 className="text-lg font-semibold mb-4">{dictionary.appointments.integrationStatus}</h3>
        <div className="flex flex-wrap gap-2">
          <Badge variant="secondary">{dictionary.appointments.selfContained}</Badge>
          {!appointment.case_file_id && (
            <Badge variant="outline">{dictionary.appointments.noCaseFileLink}</Badge>
          )}
          {!appointment.service_id && (
            <Badge variant="outline">{dictionary.appointments.noServiceLink}</Badge>
          )}
        </div>
        <p className="text-sm text-muted-foreground mt-2">
          {dictionary.appointments.integrationDescription}
        </p>
      </div>
    </div>
  );
}
