"use server";

import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";
import { auth } from "@/lib/authentication/services/AuthenticationService";
import { AppointmentService } from "../lib/services/AppointmentService";

/**
 * Delete an appointment
 */
export async function deleteAppointment(id: string) {
  try {
    // Get current user and organization
    const currentUser = await auth.getCurrentUser();
    if (!currentUser) {
      throw new Error("Authentication required");
    }

    const userProfile = await auth.getCurrentUserProfile();
    if (!userProfile?.organizationId) {
      throw new Error("Organization context required");
    }

    // Verify appointment exists and belongs to organization
    const appointmentService = new AppointmentService();
    const existingResult = await appointmentService.getAppointmentById(id);

    if (!existingResult.success) {
      throw new Error("Appointment not found");
    }

    if (existingResult.data?.organization_id !== userProfile.organizationId) {
      throw new Error("Appointment not found");
    }

    // Delete appointment using service
    const result = await appointmentService.deleteAppointment(id);

    if (!result.success) {
      throw new Error(result.message);
    }

    // Revalidate relevant paths
    revalidatePath("/protected/scheduling/appointments");
    revalidatePath("/protected/scheduling/appointments/list");
    revalidatePath("/protected/scheduling/appointments/calendar");

    // Redirect to appointments list
    redirect("/protected/scheduling/appointments/list");
  } catch (error) {
    throw error;
  }
}

/**
 * Delete appointment and return result (for modal/drawer usage)
 */
export async function deleteAppointmentAndReturn(id: string) {
  try {
    // Get current user and organization
    const currentUser = await auth.getCurrentUser();
    if (!currentUser) {
      return { success: false, error: "Authentication required" };
    }

    const userProfile = await auth.getCurrentUserProfile();
    if (!userProfile?.organizationId) {
      return { success: false, error: "Organization context required" };
    }

    // Verify appointment exists and belongs to organization
    const appointmentService = new AppointmentService();
    const existingResult = await appointmentService.getAppointmentById(id);

    if (!existingResult.success) {
      return { success: false, error: "Appointment not found" };
    }

    if (existingResult.data?.organization_id !== userProfile.organizationId) {
      return { success: false, error: "Appointment not found" };
    }

    // Delete appointment using service
    const result = await appointmentService.deleteAppointment(id);

    if (!result.success) {
      return { success: false, error: result.message };
    }

    // Revalidate relevant paths
    revalidatePath("/protected/scheduling/appointments");
    revalidatePath("/protected/scheduling/appointments/list");
    revalidatePath("/protected/scheduling/appointments/calendar");

    return {
      success: true,
      message: "Appointment deleted successfully",
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to delete appointment",
    };
  }
}
