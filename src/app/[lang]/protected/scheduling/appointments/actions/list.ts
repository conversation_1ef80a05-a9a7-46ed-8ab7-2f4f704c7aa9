"use server";

import { auth } from "@/lib/authentication/services/AuthenticationService";
import { AppointmentService } from "../lib/services/AppointmentService";
import { AppointmentFilters, AppointmentListResponse } from "../lib/types";

/**
 * List appointments with filtering and pagination
 */
export async function listAppointments(filters: AppointmentFilters = {}): Promise<{
  success: boolean;
  data?: AppointmentListResponse;
  error?: any;
  message: string;
}> {
  try {
    // Validate authentication
    const currentUser = await auth.getCurrentUser();
    if (!currentUser) {
      return {
        success: false,
        message: "Authentication required",
      };
    }

    const userProfile = await auth.getCurrentUserProfile();
    if (!userProfile?.organizationId) {
      return {
        success: false,
        message: "Organization context required",
      };
    }

    // Get appointments using service
    const appointmentService = new AppointmentService();
    const result = await appointmentService.listAppointments(userProfile.organizationId, filters);

    if (!result.success) {
      return {
        success: false,
        error: result.error,
        message: result.message,
      };
    }

    return {
      success: true,
      data: result.data || undefined,
      message: "Appointments fetched successfully",
    };
  } catch (error) {
    return {
      success: false,
      error,
      message: error instanceof Error ? error.message : "Failed to fetch appointments",
    };
  }
}

/**
 * Get appointments for calendar view (returns more appointments, less pagination)
 */
export async function getAppointmentsForCalendar(
  filters: Omit<AppointmentFilters, "page" | "limit"> = {}
) {
  return await listAppointments({
    ...filters,
    limit: 100, // Get more appointments for calendar view
    page: 1,
  });
}

/**
 * Search appointments by title or description
 */
export async function searchAppointments(
  query: string,
  filters: Omit<AppointmentFilters, "search"> = {}
) {
  return await listAppointments({
    ...filters,
    search: query,
  });
}
