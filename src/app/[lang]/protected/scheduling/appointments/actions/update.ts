"use server";

import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";
import { z } from "zod";
import { auth } from "@/lib/authentication/services/AuthenticationService";
import { AppointmentService } from "../lib/services/AppointmentService";
import { AppointmentStatus } from "../lib/types";
import { APPOINTMENT_CONFIG } from "../lib/config";

// Validation schema for appointment updates
const updateAppointmentSchema = z.object({
  title: z
    .string()
    .min(APPOINTMENT_CONFIG.validation.title.minLength, "Title is too short")
    .max(APPOINTMENT_CONFIG.validation.title.maxLength, "Title is too long")
    .optional(),
  description: z
    .string()
    .max(APPOINTMENT_CONFIG.validation.description.maxLength, "Description is too long")
    .optional(),
  appointment_date: z.string().optional(),
  start_time: z.string().optional(),
  end_time: z.string().optional(),
  status: z
    .enum(["planned", "confirmed", "in_progress", "completed", "missed", "postponed", "cancelled"])
    .optional(),
});

/**
 * Update an existing appointment
 */
export async function updateAppointment(id: string, formData: FormData) {
  try {
    // Get current user and organization
    const currentUser = await auth.getCurrentUser();
    if (!currentUser) {
      throw new Error("Authentication required");
    }

    const userProfile = await auth.getCurrentUserProfile();
    if (!userProfile?.organizationId) {
      throw new Error("Organization context required");
    }

    // Verify appointment exists and belongs to organization
    const appointmentService = new AppointmentService();
    const existingResult = await appointmentService.getAppointmentById(id);

    if (!existingResult.success) {
      throw new Error("Appointment not found");
    }

    if (existingResult.data?.organization_id !== userProfile.organizationId) {
      throw new Error("Appointment not found");
    }

    // Extract and validate form data
    const rawData = {
      title: formData.get("title") as string,
      description: formData.get("description") as string,
      appointment_date: formData.get("appointment_date") as string,
      start_time: formData.get("start_time") as string,
      end_time: formData.get("end_time") as string,
      status: formData.get("status") as string,
    };

    // Filter out empty values and validate
    const filteredData = Object.fromEntries(
      Object.entries(rawData).filter(
        ([_, value]) => value !== "" && value !== null && value !== undefined
      )
    );

    const validatedData = updateAppointmentSchema.parse(filteredData);

    // Validate time logic if both times are provided
    if (validatedData.start_time && validatedData.end_time) {
      const startTime = new Date(`2000-01-01T${validatedData.start_time}`);
      const endTime = new Date(`2000-01-01T${validatedData.end_time}`);

      if (endTime <= startTime) {
        throw new Error("End time must be after start time");
      }
    }

    // Validate appointment date is not in the past (unless same day)
    if (validatedData.appointment_date) {
      const appointmentDate = new Date(validatedData.appointment_date);
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      if (appointmentDate < today) {
        throw new Error("Cannot schedule appointments in the past");
      }
    }

    // Update appointment using service
    const result = await appointmentService.updateAppointment(id, validatedData);

    if (!result.success) {
      throw new Error(result.message);
    }

    // Revalidate relevant paths
    revalidatePath("/protected/scheduling/appointments");
    revalidatePath("/protected/scheduling/appointments/list");
    revalidatePath("/protected/scheduling/appointments/calendar");
    revalidatePath(`/protected/scheduling/appointments/${id}`);
    revalidatePath(`/protected/scheduling/appointments/${id}/view`);

    // Redirect to appointment view
    redirect(`/protected/scheduling/appointments/${id}/view`);
  } catch (error) {
    // Return error for form handling
    if (error instanceof z.ZodError) {
      const fieldErrors = error.errors
        .map((err) => `${err.path.join(".")}: ${err.message}`)
        .join(", ");
      throw new Error(`Validation failed: ${fieldErrors}`);
    }

    throw error;
  }
}

/**
 * Update appointment status only
 */
export async function updateAppointmentStatus(id: string, status: AppointmentStatus) {
  try {
    // Get current user and organization
    const currentUser = await auth.getCurrentUser();
    if (!currentUser) {
      return { success: false, error: "Authentication required" };
    }

    const userProfile = await auth.getCurrentUserProfile();
    if (!userProfile?.organizationId) {
      return { success: false, error: "Organization context required" };
    }

    // Verify appointment exists and belongs to organization
    const appointmentService = new AppointmentService();
    const existingResult = await appointmentService.getAppointmentById(id);

    if (!existingResult.success) {
      return { success: false, error: "Appointment not found" };
    }

    if (existingResult.data?.organization_id !== userProfile.organizationId) {
      return { success: false, error: "Appointment not found" };
    }

    // Update status using service
    const result = await appointmentService.updateAppointmentStatus(id, status);

    if (!result.success) {
      return { success: false, error: result.message };
    }

    // Revalidate relevant paths
    revalidatePath("/protected/scheduling/appointments");
    revalidatePath("/protected/scheduling/appointments/list");
    revalidatePath("/protected/scheduling/appointments/calendar");
    revalidatePath(`/protected/scheduling/appointments/${id}`);
    revalidatePath(`/protected/scheduling/appointments/${id}/view`);

    return {
      success: true,
      data: result.data,
      message: "Appointment status updated successfully",
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to update appointment status",
    };
  }
}
