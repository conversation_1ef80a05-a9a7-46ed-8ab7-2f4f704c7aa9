import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Calendar,
  FileText,
  Plus,
  AlertCircle,
  CheckCircle,
  Users,
  Bell,
  Activity,
  TrendingUp,
} from "lucide-react";
import { PageHeader } from "@/components";
import { dashboardService } from "./lib/services/DashboardService";
import Link from "next/link";
import { i18n } from "@/lib/i18n/services/I18nService";

interface DashboardPageProps {
  params: Promise<{ lang: string }>;
}

export default async function DashboardPage({ params }: DashboardPageProps) {
  const { lang } = await params;

  // Load translations
  const t = i18n.getDictionary(lang);

  // Fetch real appointment data
  const appointmentsResult = await dashboardService.getTodayAppointments();
  const todayAppointments = appointmentsResult.success ? appointmentsResult.data || [] : [];

  // Fetch workload summary
  const workloadResult = await dashboardService.getWorkloadSummary();
  const workloadStats =
    workloadResult.success && workloadResult.data
      ? workloadResult.data
      : {
          activeCases: 0,
          pendingRequests: 0,
          upcomingAppointments: 0,
          overdueItems: 0,
        };

  // Fetch urgent tasks
  const urgentTasksResult = await dashboardService.getUrgentTasks();
  const urgentTasks = urgentTasksResult.success ? urgentTasksResult.data || [] : [];

  // Fetch recent case files
  const recentCaseFilesResult = await dashboardService.getRecentCaseFiles();
  const recentCaseFiles = recentCaseFilesResult.success ? recentCaseFilesResult.data || [] : [];

  // Fetch recent notifications
  const notificationsResult = await dashboardService.getRecentNotifications();
  const recentNotifications = notificationsResult.success ? notificationsResult.data || [] : [];

  return (
    <div className="space-y-6">
      <PageHeader title={t.dashboard.title} description={t.dashboard.description} />

      {/* Top Row - Priority Widgets */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* My Appointments Today */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t.dashboard.widgets.myAppointmentsToday.title}
            </CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{todayAppointments.length}</div>
            <p className="text-xs text-muted-foreground">
              {lang === "fr" ? "rendez-vous planifiés" : "appointments scheduled"}
            </p>
            <div className="mt-4 space-y-2">
              {todayAppointments.length > 0 ? (
                todayAppointments.map((appointment) => (
                  <div
                    key={appointment.id}
                    className="flex items-center justify-between p-2 bg-muted/50 rounded-md"
                  >
                    <div className="flex-1">
                      <p className="text-sm font-medium">{appointment.time}</p>
                      <p className="text-xs text-muted-foreground truncate">{appointment.title}</p>
                      {appointment.room && (
                        <p className="text-xs text-muted-foreground">{appointment.room}</p>
                      )}
                    </div>
                    <Badge
                      variant={appointment.status === "confirmed" ? "default" : "secondary"}
                      className="text-xs"
                    >
                      {appointment.status === "confirmed"
                        ? lang === "fr"
                          ? "Confirmé"
                          : "Confirmed"
                        : lang === "fr"
                          ? "Planifié"
                          : "Planned"}
                    </Badge>
                  </div>
                ))
              ) : (
                <div className="text-center py-4 text-sm text-muted-foreground">
                  {t.dashboard.widgets.myAppointmentsToday.noAppointments}
                </div>
              )}
            </div>
            <Link href={`/${lang}/protected/scheduling/appointments`}>
              <Button variant="outline" size="sm" className="w-full mt-3">
                <Calendar className="h-4 w-4 mr-2" />
                {t.dashboard.widgets.myAppointmentsToday.viewAll}
              </Button>
            </Link>
          </CardContent>
        </Card>

        {/* Urgent Tasks */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t.dashboard.widgets.urgentTasks.title}
            </CardTitle>
            <AlertCircle className="h-4 w-4 text-destructive" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-destructive">{urgentTasks.length}</div>
            <p className="text-xs text-muted-foreground">
              {lang === "fr" ? "éléments nécessitent une attention" : "items need attention"}
            </p>
            <div className="mt-4 space-y-2">
              {urgentTasks.length > 0 ? (
                urgentTasks.map((task) => (
                  <div
                    key={task.id}
                    className="flex items-center justify-between p-2 bg-muted/50 rounded-md"
                  >
                    <div className="flex-1">
                      <p className="text-sm font-medium">{task.title}</p>
                      <p className="text-xs text-muted-foreground">{task.dueDate}</p>
                    </div>
                    <Badge
                      variant={task.priority === "high" ? "destructive" : "secondary"}
                      className="text-xs"
                    >
                      {t.dashboard.widgets.urgentTasks.priority[task.priority]}
                    </Badge>
                  </div>
                ))
              ) : (
                <div className="text-center py-4 text-sm text-muted-foreground">
                  {t.dashboard.widgets.urgentTasks.noTasks}
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t.dashboard.widgets.quickActions.title}
            </CardTitle>
            <Plus className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Link href={`/${lang}/protected/scheduling/appointments/create`}>
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <Calendar className="h-4 w-4 mr-2" />
                  {t.dashboard.widgets.quickActions.scheduleAppointment}
                </Button>
              </Link>
              <Link href={`/${lang}/protected/automation/request-wizard`}>
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <Plus className="h-4 w-4 mr-2" />
                  {t.dashboard.widgets.quickActions.newRequest}
                </Button>
              </Link>
              <Link href={`/${lang}/protected/contact/management/create`}>
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <Users className="h-4 w-4 mr-2" />
                  {t.dashboard.widgets.quickActions.addContact}
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Second Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Recent Case Files */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t.dashboard.widgets.recentCaseFiles.title}
            </CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recentCaseFiles.length > 0 ? (
                recentCaseFiles.map((caseFile) => (
                  <Link
                    key={caseFile.id}
                    href={`/${lang}/protected/case-file/${caseFile.id}/active`}
                    className="block"
                  >
                    <div className="flex items-center justify-between p-2 bg-muted/50 rounded-md hover:bg-muted/70 transition-colors">
                      <div className="flex-1">
                        <p className="text-sm font-medium">{caseFile.case_number}</p>
                        <p className="text-xs text-muted-foreground">{caseFile.title}</p>
                        <p className="text-xs text-muted-foreground">{caseFile.lastUpdate}</p>
                      </div>
                      <Badge
                        variant={caseFile.status === "active" ? "default" : "secondary"}
                        className="text-xs"
                      >
                        {t.dashboard.caseFileStatus[
                          caseFile.status as keyof typeof t.dashboard.caseFileStatus
                        ] || caseFile.status}
                      </Badge>
                    </div>
                  </Link>
                ))
              ) : (
                <div className="text-center py-4 text-sm text-muted-foreground">
                  {t.dashboard.widgets.recentCaseFiles.noFiles}
                </div>
              )}
            </div>
            <Link href={`/${lang}/protected/case-file`}>
              <Button variant="outline" size="sm" className="w-full mt-3">
                <FileText className="h-4 w-4 mr-2" />
                {t.dashboard.widgets.recentCaseFiles.viewAllCases}
              </Button>
            </Link>
          </CardContent>
        </Card>

        {/* Workload Summary */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t.dashboard.widgets.workloadSummary.title}
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">
                  {t.dashboard.widgets.workloadSummary.activeCases}
                </span>
                <span className="text-sm font-medium">{workloadStats.activeCases}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">
                  {t.dashboard.widgets.workloadSummary.pendingRequests}
                </span>
                <span className="text-sm font-medium">{workloadStats.pendingRequests}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">
                  {t.dashboard.widgets.workloadSummary.upcomingAppointments}
                </span>
                <span className="text-sm font-medium">{workloadStats.upcomingAppointments}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">
                  {t.dashboard.widgets.workloadSummary.overdueItems}
                </span>
                <span className="text-sm font-medium text-destructive">
                  {workloadStats.overdueItems}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Notifications */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t.dashboard.widgets.notifications.title}
            </CardTitle>
            <Bell className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recentNotifications.length > 0 ? (
                recentNotifications.map((notification) => {
                  const IconComponent =
                    notification.type === "success"
                      ? CheckCircle
                      : notification.type === "warning"
                        ? AlertCircle
                        : notification.type === "error"
                          ? AlertCircle
                          : Activity;

                  const iconColor =
                    notification.type === "success"
                      ? "text-green-600"
                      : notification.type === "warning"
                        ? "text-orange-600"
                        : notification.type === "error"
                          ? "text-red-600"
                          : "text-blue-600";

                  return (
                    <div
                      key={notification.id}
                      className="flex items-start space-x-2 p-2 bg-muted/50 rounded-md"
                    >
                      <IconComponent className={`h-4 w-4 ${iconColor} mt-0.5`} />
                      <div className="flex-1">
                        <p className="text-sm font-medium">{notification.title}</p>
                        <p className="text-xs text-muted-foreground">{notification.timestamp}</p>
                      </div>
                    </div>
                  );
                })
              ) : (
                <div className="text-center py-4 text-sm text-muted-foreground">
                  {t.dashboard.widgets.notifications.noNotifications}
                </div>
              )}
            </div>
            <Link href={`/${lang}/protected/notifications`}>
              <Button variant="outline" size="sm" className="w-full mt-3">
                <Bell className="h-4 w-4 mr-2" />
                {t.dashboard.widgets.notifications.viewAll}
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
