"use server";

import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";
import { z } from "zod";
import { RequestService } from "../lib/services/RequestService";
import { RequestStatus, RequestUpdate } from "../lib/types";
import { ActionState } from "@/lib/types/responses";
import { logger } from "@/lib/logger/services/LoggerService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { DOMAIN_PERMISSIONS } from "../lib/config/domain";
import { createClient } from "@/lib/supabase/server";
import { auth } from "@/lib/authentication/services/AuthenticationService";
import { DocumentUploadService } from "@/app/[lang]/protected/document/lib/services/DocumentUploadService";

// Define the schema for request updates
const updateRequestSchema = z.object({
  title: z.string().min(1, "Title is required").max(255, "Title is too long").optional(),
  description: z.string().min(1, "Description is required").optional(),
  service_type: z.string().min(1, "Service type is required").optional(),
  priority: z.enum(["low", "medium", "high"]).optional(),
  status: z
    .enum(["draft", "pending", "approved", "rejected", "completed", "cancelled", "waitlist"])
    .optional(),
  assignee_id: z.string().optional().nullable(),
  notes: z.string().optional().nullable(),
});

// Type for the form data
type UpdateRequestFormData = z.infer<typeof updateRequestSchema>;

/**
 * Update an existing request
 * @param id The ID of the request to update
 * @param formData The form data for the request update
 * @returns ActionState with the updated request or error
 */
export const updateRequest = requirePermission(DOMAIN_PERMISSIONS.UPDATE)(async (
  id: string,
  formData: FormData
): Promise<ActionState<{ id: string } | null>> => {
  try {
    // Extract and validate form data
    const rawData = Object.fromEntries(formData.entries());
    const validationResult = updateRequestSchema.safeParse(rawData);

    if (!validationResult.success) {
      const errors = validationResult.error.format();
      return {
        success: false,
        error: "Validation failed: " + JSON.stringify(errors),
        data: null,
      };
    }

    const data = validationResult.data;

    // Prepare request update data
    const requestData: RequestUpdate = {
      ...data,
      status: data.status as RequestStatus,
      updated_at: new Date().toISOString(),
    };

    // Update the request
    const response = await RequestService.update(id, requestData);

    if (!response.success || !response.data) {
      return {
        success: false,
        error: response.message || "Failed to update request",
        data: null,
      };
    }

    // Revalidate the requests list page and the specific request page
    revalidatePath("/[lang]/protected/request", "layout");
    revalidatePath(`/[lang]/protected/request/[id]`, "page");

    return {
      success: true,
      error: "",
      data: { id: response.data.id },
    };
  } catch (error) {
    logger.error(`Unexpected error updating request: ${error}`);
    return {
      success: false,
      error: `Unexpected error updating request: ${error}`,
      data: null,
    };
  }
});

/**
 * Update a request and redirect to the request page
 * @param id The ID of the request to update
 * @param formData The form data for the request update
 * @param lang The language code for the redirect URL
 */
export const updateRequestAndRedirect = requirePermission(DOMAIN_PERMISSIONS.UPDATE)(async (
  id: string,
  formData: FormData,
  lang: string = "en"
): Promise<ActionState<{ id: string } | null>> => {
  const result = await updateRequest(id, formData);

  if (result.success && result.data) {
    // Redirect to the request view page
    redirect(`/${lang}/protected/request/${result.data.id}`);
  }

  return result;
});

/**
 * Update the status of a request
 * @param id The ID of the request
 * @param status The new status
 * @returns ActionState with the updated request or error
 */
export const updateRequestStatus = requirePermission(DOMAIN_PERMISSIONS.UPDATE)(async (
  id: string,
  status: RequestStatus
): Promise<ActionState<{ id: string } | null>> => {
  try {
    // Prepare request update data
    const requestData: RequestUpdate = {
      status,
      status_updated_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    // Update the request
    const response = await RequestService.update(id, requestData);

    if (!response.success || !response.data) {
      return {
        success: false,
        error: response.message || "Failed to update request status",
        data: null,
      };
    }

    // NEW: When status becomes "completed", create case file + document
    if (status === "completed" && response.success) {
      try {
        await createCaseFileWithDocument(id);
      } catch (error) {
        // Log error but don't fail the status update
        logger.error(`Failed to create case file for request ${id}: ${error}`);
      }
    }

    // Revalidate the requests list page and the specific request page
    revalidatePath("/[lang]/protected/request", "layout");
    revalidatePath(`/[lang]/protected/request/[id]`, "page");

    return {
      success: true,
      error: "",
      data: { id: response.data.id },
    };
  } catch (error) {
    logger.error(`Unexpected error updating request status: ${error}`);
    return {
      success: false,
      error: `Unexpected error updating request status: ${error}`,
      data: null,
    };
  }
});

/**
 * Create case file with document when request is completed
 * Ultra simple MVP implementation
 */
async function createCaseFileWithDocument(requestId: string) {
  const userProfile = await auth.getCurrentUserProfile();
  if (!userProfile?.organizationId) {
    logger.error("No organization context for case file creation");
    return;
  }

  try {
    // 1. Get request details with contacts
    const supabase = await createClient();
    const { data: request, error: requestError } = await supabase
      .from("requests")
      .select(
        `
        *,
        services(*),
        request_contacts(
          id,
          contact_id,
          relationship_type,
          contacts(*)
        )
      `
      )
      .eq("id", requestId)
      .single();

    if (requestError || !request) {
      logger.error(`Failed to get request details: ${requestError?.message}`);
      return;
    }

    // Find primary contact (first contact or one with specific relationship)
    const primaryContactRelation =
      request.request_contacts?.find(
        (rc: any) => rc.relationship_type === "primary" || rc.relationship_type === "family"
      ) || request.request_contacts?.[0];

    const primaryContact = primaryContactRelation?.contacts;

    // 2. Create case file
    const { data: caseFile, error: caseFileError } = await supabase
      .from("case_files")
      .insert({
        organization_id: userProfile.organizationId,
        request_id: requestId,
        case_number: `CF-${Date.now()}`, // Simple case number
        status: "opening",
        created_by: userProfile.id,
        metadata: {
          auto_generated: true,
          generated_from_request: requestId,
          generated_at: new Date().toISOString(),
          primary_contact_id: primaryContact?.id || null,
          service_id: request.service_id,
        },
      })
      .select()
      .single();

    if (caseFileError || !caseFile) {
      logger.error(`Failed to create case file: ${caseFileError?.message}`);
      return;
    }

    logger.info(`Case file ${caseFile.case_number} created for request ${requestId}`);

    // 3. Copy contacts from request to case file
    const caseFileContacts = await copyContactsToCaseFile(
      caseFile.id,
      request.request_contacts,
      userProfile.organizationId
    );

    // 4. Generate simple service agreement document
    await generateServiceAgreement(caseFile, request, caseFileContacts);

    // Revalidate case files page
    revalidatePath("/[lang]/protected/case-file", "layout");
  } catch (error) {
    logger.error(`Error in createCaseFileWithDocument: ${error}`);
  }
}

/**
 * Copy contacts from request to case file
 */
async function copyContactsToCaseFile(
  caseFileId: string,
  requestContacts: any[],
  organizationId: string
) {
  try {
    const supabase = await createClient();

    if (!requestContacts || requestContacts.length === 0) {
      logger.info("No contacts to copy to case file");
      return [];
    }

    // Create case_file_contacts entries
    const caseFileContactsData = requestContacts
      .filter((rc: any) => {
        if (!rc.contacts?.id) {
          logger.warn(`Skipping request contact ${rc.id} - missing contact data`);
          return false;
        }
        return true;
      })
      .map((rc: any) => ({
        organization_id: organizationId,
        case_file_id: caseFileId,
        contact_id: rc.contacts.id, // Get contact ID from nested contacts object
        relationship_type: rc.relationship_type,
      }));

    if (caseFileContactsData.length === 0) {
      logger.warn(`No valid contacts found for case file ${caseFileId}`);
      return [];
    }

    // First, insert the case_file_contacts records
    const { data: insertedContacts, error: insertError } = await supabase
      .from("case_file_contacts")
      .insert(caseFileContactsData)
      .select("*");

    if (insertError) {
      logger.error(`Failed to copy contacts to case file: ${insertError.message}`);
      return [];
    }

    // Fetch case_file_contacts and contacts separately to avoid relationship ambiguity
    const { data: caseFileContactsBasic, error: fetchError } = await supabase
      .from("case_file_contacts")
      .select("id, case_file_id, contact_id, relationship_type, organization_id, created_at")
      .eq("case_file_id", caseFileId);

    if (fetchError) {
      logger.error(`Failed to fetch case file contacts: ${fetchError.message}`);
      return insertedContacts || [];
    }

    if (!caseFileContactsBasic || caseFileContactsBasic.length === 0) {
      logger.warn(`No case file contacts found for case file ${caseFileId}`);
      return [];
    }

    // Fetch contact details separately
    const contactIds = caseFileContactsBasic.map((cfc) => cfc.contact_id);
    const { data: contacts, error: contactsError } = await supabase
      .from("contacts")
      .select("id, name, email, phone, address")
      .in("id", contactIds);

    if (contactsError) {
      logger.error(`Failed to fetch contact details: ${contactsError.message}`);
      return insertedContacts || [];
    }

    // Manually join the data
    const caseFileContacts = caseFileContactsBasic.map((cfc) => ({
      ...cfc,
      contacts: contacts?.find((c) => c.id === cfc.contact_id) || null,
    }));

    // Debug: Log the joined data
    logger.info(`Fetched ${caseFileContacts.length} case file contacts with details`);
    caseFileContacts.forEach((cfc, index) => {
      logger.info(
        `Contact ${index + 1}: ${JSON.stringify({
          id: cfc.id,
          contact_id: cfc.contact_id,
          relationship_type: cfc.relationship_type,
          contact_name: cfc.contacts?.name,
          contact_email: cfc.contacts?.email,
          contact_phone: cfc.contacts?.phone,
          contact_address: cfc.contacts?.address,
        })}`
      );
    });

    logger.info(`Copied ${insertedContacts?.length || 0} contacts to case file ${caseFileId}`);
    return caseFileContacts || [];
  } catch (error) {
    logger.error(`Error in copyContactsToCaseFile: ${error}`);
    return [];
  }
}

/**
 * Generate a simple service agreement document using the upload API
 */
async function generateServiceAgreement(caseFile: any, request: any, caseFileContacts: any[]) {
  try {
    const supabase = await createClient();

    // Find any template with "service" or "agreement" in the name
    const { data: template, error: templateError } = await supabase
      .from("document_templates")
      .select("*")
      .eq("organization_id", caseFile.organization_id)
      .eq("is_active", true)
      .or("name.ilike.%service%,name.ilike.%agreement%")
      .limit(1)
      .single();

    if (templateError || !template) {
      logger.info("No service template found, skipping document generation");
      return;
    }

    // Generate one document per contact
    const generatedDocuments = [];
    const currentDate = new Date().toLocaleDateString();
    const orgName = "RQRSDA Montreal";

    for (const caseFileContact of caseFileContacts) {
      try {
        const contactName = caseFileContact.contacts?.name || "Contact Name";

        // Extract email from JSON structure
        let contactEmail = "";
        if (caseFileContact.contacts?.email) {
          const emailData =
            typeof caseFileContact.contacts.email === "string"
              ? JSON.parse(caseFileContact.contacts.email)
              : caseFileContact.contacts.email;
          contactEmail = emailData?.personal || emailData?.work || emailData?.email || "";
        }

        // Extract phone from JSON structure
        let contactPhone = "";
        if (caseFileContact.contacts?.phone) {
          const phoneData =
            typeof caseFileContact.contacts.phone === "string"
              ? JSON.parse(caseFileContact.contacts.phone)
              : caseFileContact.contacts.phone;
          contactPhone = phoneData?.mobile || phoneData?.home || phoneData?.phone || "";
        }

        const contactAddress = caseFileContact.contacts?.address || "";

        // Create customized content for this specific contact
        let content = template.content;

        // Debug: Log template content and contact data
        logger.info(`Template content preview: ${content.substring(0, 200)}...`);
        logger.info(
          `Contact data: name=${contactName}, email=${contactEmail}, phone=${contactPhone}, address=${contactAddress}`
        );

        // Replace tokens with this contact's specific information using flexible regex (handles spaces)
        content = content.replace(/\{\{\s*contact\.full_name\s*\}\}/g, contactName);
        content = content.replace(/\{\{\s*contact\.name\s*\}\}/g, contactName); // Support both formats
        content = content.replace(
          /\{\{\s*contact\.first_name\s*\}\}/g,
          contactName.split(" ")[0] || ""
        );
        content = content.replace(
          /\{\{\s*contact\.last_name\s*\}\}/g,
          contactName.split(" ").slice(1).join(" ") || ""
        );
        content = content.replace(/\{\{\s*contact\.email\s*\}\}/g, contactEmail);
        content = content.replace(/\{\{\s*contact\.phone\s*\}\}/g, contactPhone);
        content = content.replace(/\{\{\s*contact\.address\s*\}\}/g, contactAddress);
        content = content.replace(
          /\{\{\s*contact\.relationship\s*\}\}/g,
          caseFileContact.relationship_type
        );
        content = content.replace(/\{\{\s*case\.case_number\s*\}\}/g, caseFile.case_number);
        content = content.replace(/\{\{\s*document\.date\s*\}\}/g, currentDate);
        content = content.replace(/\{\{\s*date\.current\s*\}\}/g, currentDate); // Support both formats
        content = content.replace(/\{\{\s*organization\.name\s*\}\}/g, orgName);
        content = content.replace(/\{\{\s*document\.reference\s*\}\}/g, caseFile.case_number);
        content = content.replace(
          /\{\{\s*document\.title\s*\}\}/g,
          `Service Agreement - ${contactName}`
        );

        // Debug: Log content after token replacement
        logger.info(
          `Content after token replacement (first 300 chars): ${content.substring(0, 300)}...`
        );

        // Create a PDF file for this specific contact
        const fileName = `${caseFile.case_number}-service-agreement-${contactName.replace(/\s+/g, "-")}-${Date.now()}.pdf`;
        const file = await DocumentUploadService.createPdfFromHtml(content, fileName, {
          title: `Service Agreement - ${contactName} (${caseFileContact.relationship_type})`,
          author: "RQRSDA Montreal",
          subject: `Service Agreement for ${contactName} - Case ${caseFile.case_number}`,
        });

        // Prepare metadata for this contact's document
        const additionalMetadata = {
          template_id: template.id,
          template_name: template.name,
          generated_at: new Date().toISOString(),
          auto_generated: true,
          source_request_id: request.id,
          case_file_contact_id: caseFileContact.id, // Link to specific case_file_contacts junction record
          contact_name: contactName,
          relationship_type: caseFileContact.relationship_type,
        };

        // Upload document linked to the specific case_file_contacts junction record
        const uploadResult = await DocumentUploadService.uploadDocument({
          file,
          documentName: `Service Agreement - ${contactName} (${caseFileContact.relationship_type}) - ${caseFile.case_number}`,
          attachedToType: "case_file_contacts", // Link to junction table
          attachedToId: caseFileContact.id, // The case_file_contacts.id (junction table ID)
          category: "service_agreement",
          description: `Auto-generated service agreement document for ${contactName}`,
          tags: [
            "auto-generated",
            "service-agreement",
            template.name,
            caseFileContact.relationship_type,
          ],
          templateId: template.id,
          contactId: caseFileContact.contact_id, // Link to the actual contact
          attachmentType: "generated",
          additionalMetadata,
        });

        if (uploadResult.success) {
          generatedDocuments.push({
            contactName,
            relationshipType: caseFileContact.relationship_type,
            documentId: uploadResult.data?.id,
          });
          logger.info(
            `Service agreement generated for ${contactName} (${caseFileContact.relationship_type})`
          );
        } else {
          logger.error(`Failed to upload document for ${contactName}: ${uploadResult.message}`);
        }
      } catch (error) {
        logger.error(
          `Error generating document for contact ${caseFileContact.contacts?.name}: ${error}`
        );
      }
    }

    logger.info(
      `Generated ${generatedDocuments.length} service agreement documents for case file ${caseFile.case_number}`
    );
    generatedDocuments.forEach((doc) => {
      logger.info(`- ${doc.contactName} (${doc.relationshipType}): Document ID ${doc.documentId}`);
    });
  } catch (error) {
    logger.error(`Error in generateServiceAgreement: ${error}`);
  }
}
