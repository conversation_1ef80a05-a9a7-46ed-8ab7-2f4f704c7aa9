/**
 * Document Domain Configuration
 */

export const DOMAIN_ID = "document";
export const DOMAIN_NAME = "Document";
export const DOMAIN_BASE_PATH = "/protected/document";
export const DOMAIN_DESCRIPTION = "Document attachment and management system";

export const DOMAIN_PERMISSIONS = {
  ATTACHMENT: {
    VIEW: `${DOMAIN_ID}:attachment:view`,
    CREATE: `${DOMAIN_ID}:attachment:create`,
    UPDATE: `${DOMAIN_ID}:attachment:update`,
    DELETE: `${DOMAIN_ID}:attachment:delete`,
    DOWNLOAD: `${DOMAIN_ID}:attachment:download`,
    ALL: `${DOMAIN_ID}:attachment:*`,
  },
  TEMPLATE: {
    VIEW: `${DOMAIN_ID}:template:view`,
    CREATE: `${DOMAIN_ID}:template:create`,
    UPDATE: `${DOMAIN_ID}:template:update`,
    DELETE: `${DOMAIN_ID}:template:delete`,
    ALL: `${DOMAIN_ID}:template:*`,
  },
  VIEW: `${DOMAIN_ID}:view`,
  ADMIN: `${DOMAIN_ID}:admin`,
  ALL: `${DOMAIN_ID}:*`,
};

export const DOMAIN_ROUTE_PERMISSIONS = {
  [`/[lang]${DOMAIN_BASE_PATH}/attachments`]: DOMAIN_PERMISSIONS.ATTACHMENT.VIEW,
  [`/[lang]${DOMAIN_BASE_PATH}/attachments/upload`]: DOMAIN_PERMISSIONS.ATTACHMENT.CREATE,
  [`/[lang]${DOMAIN_BASE_PATH}/attachments/[id]`]: DOMAIN_PERMISSIONS.ATTACHMENT.VIEW,
  [`/[lang]${DOMAIN_BASE_PATH}/attachments/[id]/download`]: DOMAIN_PERMISSIONS.ATTACHMENT.DOWNLOAD,
};

const DOMAIN_CONFIG = {
  id: DOMAIN_ID,
  name: DOMAIN_NAME,
  basePath: DOMAIN_BASE_PATH,
  description: DOMAIN_DESCRIPTION,
  permissions: DOMAIN_PERMISSIONS,
  routePermissions: DOMAIN_ROUTE_PERMISSIONS,
};

export default DOMAIN_CONFIG;
