import jsPDF from "jspdf";
import { logger } from "@/lib/logger/services/LoggerService";

export interface PdfGenerationOptions {
  title?: string;
  author?: string;
  subject?: string;
  keywords?: string;
  margin?: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
}

/**
 * Service for generating PDF documents from HTML content
 */
export class PdfGenerationService {
  /**
   * Convert HTML content to PDF buffer
   */
  static async generatePdfFromHtml(
    htmlContent: string,
    options: PdfGenerationOptions = {}
  ): Promise<Buffer> {
    try {
      // Create new PDF document
      const pdf = new jsPDF({
        orientation: "portrait",
        unit: "mm",
        format: "a4",
      });

      // Set document metadata
      if (options.title) pdf.setProperties({ title: options.title });
      if (options.author) pdf.setProperties({ author: options.author });
      if (options.subject) pdf.setProperties({ subject: options.subject });
      if (options.keywords) pdf.setProperties({ keywords: options.keywords });

      // Set margins (default: 20mm on all sides)
      const margin = options.margin || { top: 20, right: 20, bottom: 20, left: 20 };

      // Calculate usable page dimensions
      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();
      const contentWidth = pageWidth - margin.left - margin.right;
      const contentHeight = pageHeight - margin.top - margin.bottom;

      // Process HTML content and preserve basic formatting
      const processedContent = this.processHtmlContent(htmlContent);

      // Split content into lines that fit the page width
      const lines = this.splitTextIntoLines(pdf, processedContent, contentWidth);

      // Add content to PDF
      let currentY = margin.top;
      const lineHeight = 7; // mm

      for (const line of lines) {
        // Check if we need a new page
        if (currentY + lineHeight > pageHeight - margin.bottom) {
          pdf.addPage();
          currentY = margin.top;
        }

        // Add the line to the PDF
        pdf.text(line, margin.left, currentY);
        currentY += lineHeight;
      }

      // Convert to buffer
      const pdfBuffer = Buffer.from(pdf.output("arraybuffer"));

      logger.info("PDF generated successfully");
      return pdfBuffer;
    } catch (error) {
      logger.error("Error generating PDF:", error as Error);
      throw new Error(
        `PDF generation failed: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  }

  /**
   * Process HTML content and preserve formatting for PDF
   */
  private static processHtmlContent(html: string): string {
    // First, handle common HTML structures and preserve formatting
    let processed = html
      // Handle headings with emphasis
      .replace(/<h[1-6][^>]*>(.*?)<\/h[1-6]>/gi, "\n\n*** $1 ***\n\n")
      // Handle paragraphs
      .replace(/<p[^>]*>(.*?)<\/p>/gi, "$1\n\n")
      // Handle line breaks
      .replace(/<br\s*\/?>/gi, "\n")
      // Handle divs
      .replace(/<div[^>]*>(.*?)<\/div>/gi, "$1\n")
      // Handle strong/bold
      .replace(/<(strong|b)[^>]*>(.*?)<\/(strong|b)>/gi, "**$2**")
      // Handle emphasis/italic
      .replace(/<(em|i)[^>]*>(.*?)<\/(em|i)>/gi, "*$2*")
      // Handle lists
      .replace(/<ul[^>]*>/gi, "\n")
      .replace(/<\/ul>/gi, "\n")
      .replace(/<ol[^>]*>/gi, "\n")
      .replace(/<\/ol>/gi, "\n")
      .replace(/<li[^>]*>(.*?)<\/li>/gi, "• $1\n")
      // Handle tables (basic)
      .replace(/<table[^>]*>/gi, "\n")
      .replace(/<\/table>/gi, "\n")
      .replace(/<tr[^>]*>/gi, "")
      .replace(/<\/tr>/gi, "\n")
      .replace(/<t[hd][^>]*>(.*?)<\/t[hd]>/gi, "$1 | ")
      // Remove remaining HTML tags
      .replace(/<[^>]*>/g, "")
      // Handle HTML entities
      .replace(/&nbsp;/g, " ")
      .replace(/&amp;/g, "&")
      .replace(/&lt;/g, "<")
      .replace(/&gt;/g, ">")
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/&hellip;/g, "...")
      // Clean up multiple newlines
      .replace(/\n\s*\n\s*\n/g, "\n\n")
      .trim();

    return processed;
  }

  /**
   * Strip HTML tags from content and preserve basic formatting (legacy method)
   */
  private static stripHtmlTags(html: string): string {
    // Replace common HTML elements with text equivalents
    let text = html
      .replace(/<br\s*\/?>/gi, "\n")
      .replace(/<\/p>/gi, "\n\n")
      .replace(/<\/div>/gi, "\n")
      .replace(/<\/h[1-6]>/gi, "\n\n")
      .replace(/<li>/gi, "• ")
      .replace(/<\/li>/gi, "\n")
      .replace(/<[^>]*>/g, "") // Remove all remaining HTML tags
      .replace(/&nbsp;/g, " ")
      .replace(/&amp;/g, "&")
      .replace(/&lt;/g, "<")
      .replace(/&gt;/g, ">")
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/\n\s*\n/g, "\n\n") // Clean up multiple newlines
      .trim();

    return text;
  }

  /**
   * Split text into lines that fit within the specified width
   */
  private static splitTextIntoLines(pdf: jsPDF, text: string, maxWidth: number): string[] {
    const lines: string[] = [];
    const paragraphs = text.split("\n");

    for (const paragraph of paragraphs) {
      if (paragraph.trim() === "") {
        lines.push(""); // Preserve empty lines
        continue;
      }

      // Split long paragraphs into multiple lines
      const words = paragraph.split(" ");
      let currentLine = "";

      for (const word of words) {
        const testLine = currentLine ? `${currentLine} ${word}` : word;
        const lineWidth = pdf.getTextWidth(testLine);

        if (lineWidth <= maxWidth) {
          currentLine = testLine;
        } else {
          if (currentLine) {
            lines.push(currentLine);
            currentLine = word;
          } else {
            // Word is too long, split it
            lines.push(word);
          }
        }
      }

      if (currentLine) {
        lines.push(currentLine);
      }
    }

    return lines;
  }

  /**
   * Create a File object from PDF buffer
   */
  static createPdfFile(pdfBuffer: Buffer, fileName: string): File {
    const blob = new Blob([pdfBuffer], { type: "application/pdf" });
    return new File([blob], fileName, { type: "application/pdf" });
  }
}
