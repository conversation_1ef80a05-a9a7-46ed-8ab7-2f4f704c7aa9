"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Eye, Code, FileText, Calendar, User, Settings } from "lucide-react";
import { DocumentTemplateWithMetadata } from "../lib/types";

interface TemplateDetailProps {
  template: DocumentTemplateWithMetadata;
}

export function TemplateDetail({ template }: TemplateDetailProps) {
  const [viewMode, setViewMode] = useState<"preview" | "source">("preview");

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      legal: "bg-blue-100 text-blue-800",
      administrative: "bg-green-100 text-green-800",
      communication: "bg-purple-100 text-purple-800",
      report: "bg-orange-100 text-orange-800",
      form: "bg-yellow-100 text-yellow-800",
      contract: "bg-red-100 text-red-800",
      other: "bg-gray-100 text-gray-800",
    };
    return colors[category] || colors.other;
  };

  return (
    <div className="space-y-6">
      {/* Header Information */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <h2 className="text-2xl font-bold">{template.name}</h2>
                <Badge variant={template.is_active ? "default" : "secondary"}>
                  {template.is_active ? "Active" : "Inactive"}
                </Badge>
              </div>
              {/* {template.description && (
                <p className="text-muted-foreground">{template.description}</p>
              )} */}
            </div>
            {/* <Badge className={getCategoryColor(template.category)}>
              {template.category.charAt(0).toUpperCase() + template.category.slice(1)}
            </Badge> */}
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Metadata */}
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2 flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  Created
                </h4>
                {template.created_at && (
                  <p className="text-sm text-muted-foreground">{formatDate(template.created_at)}</p>
                )}
                {template.created_by_name && (
                  <p className="text-sm text-muted-foreground flex items-center gap-1">
                    <User className="h-3 w-3" />
                    by {template.created_by_name}
                  </p>
                )}
              </div>

              <div>
                <h4 className="font-medium mb-2 flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  Last Updated
                </h4>
                {template.updated_at && (
                  <p className="text-sm text-muted-foreground">{formatDate(template.updated_at)}</p>
                )}
                {template.updated_by_name && (
                  <p className="text-sm text-muted-foreground flex items-center gap-1">
                    <User className="h-3 w-3" />
                    by {template.updated_by_name}
                  </p>
                )}
              </div>
            </div>

            {/* Tags */}
            {/* {template.tags && template.tags.length > 0 && (
              <div>
                <h4 className="font-medium mb-2 flex items-center gap-2">
                  <Tag className="h-4 w-4" />
                  Tags
                </h4>
                <div className="flex flex-wrap gap-2">
                  {template.tags.map((tag) => (
                    <Badge key={tag} variant="outline">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            )} */}
          </div>
        </CardContent>
      </Card>

      {/* Template Content */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Template Content
            </CardTitle>
            <div className="flex items-center gap-2">
              <Button
                variant={viewMode === "preview" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("preview")}
              >
                <Eye className="h-4 w-4 mr-1" />
                Preview
              </Button>
              <Button
                variant={viewMode === "source" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("source")}
              >
                <Code className="h-4 w-4 mr-1" />
                Source
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {viewMode === "preview" ? (
            <div
              className="min-h-[400px] p-4 border rounded-md bg-background prose prose-sm max-w-none"
              dangerouslySetInnerHTML={{ __html: template.content }}
            />
          ) : (
            <div className="min-h-[400px] p-4 border rounded-md bg-muted font-mono text-sm whitespace-pre-wrap">
              {template.content}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Template Information Tabs */}
      <Tabs defaultValue="tokens" className="w-full">
        <TabsList>
          <TabsTrigger value="tokens">Available Tokens</TabsTrigger>
          <TabsTrigger value="usage">Usage Guidelines</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="tokens" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Token Reference</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  The following tokens can be used in this template. They will be replaced with
                  actual values when generating documents.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium mb-2">Contact Information</h4>
                    <div className="space-y-1 text-sm">
                      <code className="bg-muted px-2 py-1 rounded">
                        {"{{ contact.full_name }}"}
                      </code>
                      <code className="bg-muted px-2 py-1 rounded">{"{{ contact.email }}"}</code>
                      <code className="bg-muted px-2 py-1 rounded">{"{{ contact.phone }}"}</code>
                      <code className="bg-muted px-2 py-1 rounded">{"{{ contact.address }}"}</code>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium mb-2">Organization</h4>
                    <div className="space-y-1 text-sm">
                      <code className="bg-muted px-2 py-1 rounded">
                        {"{{ organization.name }}"}
                      </code>
                      <code className="bg-muted px-2 py-1 rounded">
                        {"{{ organization.address }}"}
                      </code>
                      <code className="bg-muted px-2 py-1 rounded">{"{{ employee.name }}"}</code>
                      <code className="bg-muted px-2 py-1 rounded">{"{{ employee.title }}"}</code>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium mb-2">Document</h4>
                    <div className="space-y-1 text-sm">
                      <code className="bg-muted px-2 py-1 rounded">{"{{ document.date }}"}</code>
                      <code className="bg-muted px-2 py-1 rounded">
                        {"{{ document.reference }}"}
                      </code>
                      <code className="bg-muted px-2 py-1 rounded">{"{{ document.title }}"}</code>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium mb-2">Custom Fields</h4>
                    <div className="space-y-1 text-sm">
                      <code className="bg-muted px-2 py-1 rounded">{"{{ custom.field1 }}"}</code>
                      <code className="bg-muted px-2 py-1 rounded">{"{{ custom.field2 }}"}</code>
                      <code className="bg-muted px-2 py-1 rounded">{"{{ custom.notes }}"}</code>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="usage" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Usage Guidelines</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">How to Use This Template</h4>
                <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                  <li>
                    Use the "Generate Document" feature to create documents from this template
                  </li>
                  <li>Fill in the required token values when generating</li>
                  <li>Preview the document before finalizing</li>
                  <li>Export in your preferred format (HTML, PDF, or DOCX)</li>
                </ul>
              </div>

              <Separator />

              <div>
                <h4 className="font-medium mb-2">Best Practices</h4>
                <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                  <li>Keep token names descriptive and consistent</li>
                  <li>Test the template with sample data before using in production</li>
                  <li>Use proper formatting for dates and numbers</li>
                  <li>Include fallback text for optional tokens</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Template Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Template ID:</span>
                  <p className="text-muted-foreground font-mono">{template.id}</p>
                </div>
                <div>
                  <span className="font-medium">Category:</span>
                  {/* <p className="text-muted-foreground">{template.category}</p> */}
                </div>
                <div>
                  <span className="font-medium">Status:</span>
                  <p className="text-muted-foreground">
                    {template.is_active ? "Active" : "Inactive"}
                  </p>
                </div>
                <div>
                  <span className="font-medium">Organization:</span>
                  <p className="text-muted-foreground font-mono">{template.organization_id}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
