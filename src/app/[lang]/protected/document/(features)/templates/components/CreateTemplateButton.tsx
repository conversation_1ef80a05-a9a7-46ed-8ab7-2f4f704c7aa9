"use client";

import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";

interface CreateTemplateButtonProps {
  className?: string;
  variant?: "default" | "outline" | "secondary" | "ghost" | "link" | "destructive";
  size?: "default" | "sm" | "lg" | "icon";
}

export function CreateTemplateButton({
  className,
  variant = "default",
  size = "default",
}: CreateTemplateButtonProps) {
  return (
    <Button asChild variant={variant} size={size} className={className}>
      <Link href="/protected/document/templates/create">
        <Plus className="h-4 w-4 mr-2" />
        New Template
      </Link>
    </Button>
  );
}
