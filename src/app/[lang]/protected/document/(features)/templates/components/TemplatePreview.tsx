"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Eye, FileText, Download } from "lucide-react";
import { DocumentTemplateWithMetadata, TemplateTokenGroup } from "../lib/types";

interface TemplatePreviewProps {
  template: DocumentTemplateWithMetadata;
  availableTokens: TemplateTokenGroup[];
}

export function TemplatePreview({ template, availableTokens }: TemplatePreviewProps) {
  const [tokenValues, setTokenValues] = useState<Record<string, any>>({});
  const [previewContent, setPreviewContent] = useState(template.content);

  // Generate preview with token replacement
  const generatePreview = () => {
    let content = template.content;

    // Replace tokens with values
    Object.entries(tokenValues).forEach(([key, value]) => {
      const tokenPattern = new RegExp(`{{\\s*${key}\\s*}}`, "g");
      content = content.replace(tokenPattern, String(value || `[${key}]`));
    });

    setPreviewContent(content);
  };

  // Handle token value change
  const handleTokenChange = (tokenKey: string, value: any) => {
    setTokenValues((prev) => ({
      ...prev,
      [tokenKey]: value,
    }));
  };

  // Get sample data for tokens
  const getSampleData = () => {
    const sampleData: Record<string, any> = {
      "contact.full_name": "Jean Dupont",
      "contact.first_name": "Jean",
      "contact.last_name": "Dupont",
      "contact.email": "<EMAIL>",
      "contact.phone": "(*************",
      "contact.address": "123 Rue Principale, Montréal, QC H1A 1A1",
      "contact.date_of_birth": "1985-06-15",
      "organization.name": "Centre de Services Sociaux",
      "organization.address": "456 Boulevard Central, Montréal, QC H2B 2B2",
      "employee.name": "Marie Tremblay",
      "employee.title": "Travailleuse sociale",
      "employee.email": "<EMAIL>",
      "document.date": new Date().toLocaleDateString("fr-CA"),
      "document.reference": "DOC-2024-001",
      "document.title": template.name,
      "custom.field1": "Valeur personnalisée 1",
      "custom.field2": "Valeur personnalisée 2",
      "custom.field3": "Valeur personnalisée 3",
      "custom.notes": "Notes additionnelles pour ce document.",
    };

    setTokenValues(sampleData);

    // Generate preview with sample data
    let content = template.content;
    Object.entries(sampleData).forEach(([key, value]) => {
      const tokenPattern = new RegExp(`{{\\s*${key}\\s*}}`, "g");
      content = content.replace(tokenPattern, String(value));
    });
    setPreviewContent(content);
  };

  // Render input for token based on type
  const renderTokenInput = (token: any) => {
    const value = tokenValues[token.key] || "";

    switch (token.type) {
      case "date":
        return (
          <Input
            type="date"
            value={value}
            onChange={(e) => handleTokenChange(token.key, e.target.value)}
          />
        );
      case "number":
        return (
          <Input
            type="number"
            value={value}
            onChange={(e) => handleTokenChange(token.key, e.target.value)}
          />
        );
      case "boolean":
        return (
          <Select
            value={value.toString()}
            onValueChange={(val) => handleTokenChange(token.key, val === "true")}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="true">Oui</SelectItem>
              <SelectItem value="false">Non</SelectItem>
            </SelectContent>
          </Select>
        );
      default:
        return (
          <Textarea
            value={value}
            onChange={(e) => handleTokenChange(token.key, e.target.value)}
            placeholder={`Entrer ${token.label.toLowerCase()}`}
            rows={2}
          />
        );
    }
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Token Values Input */}
      <div className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Token Values</CardTitle>
            <div className="flex gap-2">
              <Button onClick={getSampleData} size="sm" variant="outline">
                <FileText className="h-4 w-4 mr-1" />
                Sample Data
              </Button>
              <Button onClick={generatePreview} size="sm">
                <Eye className="h-4 w-4 mr-1" />
                Generate Preview
              </Button>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <Tabs
              defaultValue={availableTokens[0]?.name.toLowerCase().replace(/\s+/g, "-")}
              className="w-full"
            >
              <TabsList className="grid w-full grid-cols-2 mx-4 mb-4">
                {availableTokens.slice(0, 2).map((group) => (
                  <TabsTrigger
                    key={group.name}
                    value={group.name.toLowerCase().replace(/\s+/g, "-")}
                    className="text-xs"
                  >
                    {group.name.split(" ")[0]}
                  </TabsTrigger>
                ))}
              </TabsList>

              {availableTokens.map((group) => (
                <TabsContent
                  key={group.name}
                  value={group.name.toLowerCase().replace(/\s+/g, "-")}
                  className="space-y-3 px-4 pb-4 max-h-[500px] overflow-y-auto"
                >
                  <div className="space-y-2">
                    <h4 className="font-medium text-sm">{group.name}</h4>
                    <p className="text-xs text-muted-foreground">{group.description}</p>
                  </div>

                  <div className="space-y-3">
                    {group.tokens.map((token) => (
                      <div key={token.key} className="space-y-1">
                        <Label className="text-xs font-medium">{token.label}</Label>
                        {renderTokenInput(token)}
                      </div>
                    ))}
                  </div>
                </TabsContent>
              ))}
            </Tabs>
          </CardContent>
        </Card>
      </div>

      {/* Preview */}
      <div className="lg:col-span-2">
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Template Preview</CardTitle>
              <div className="flex gap-2">
                <Button size="sm" variant="outline">
                  <Download className="h-4 w-4 mr-1" />
                  Export PDF
                </Button>
                <Button size="sm" variant="outline">
                  <Download className="h-4 w-4 mr-1" />
                  Export DOCX
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div
              className="min-h-[600px] p-6 border rounded-md bg-white prose max-w-none"
              dangerouslySetInnerHTML={{ __html: previewContent }}
              style={{
                fontFamily: "system-ui, -apple-system, sans-serif",
                lineHeight: "1.6",
                color: "#333",
              }}
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
