"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import {
  FileText,
  Download,
  Eye,
  Settings,
  Calendar,
  User,
  Building,
  FileEdit,
} from "lucide-react";
import { DocumentTemplateWithMetadata, TemplateTokenGroup } from "../lib/types";
import { generateDocument } from "../actions/generate";

interface DocumentGeneratorProps {
  template: DocumentTemplateWithMetadata;
  availableTokens?: TemplateTokenGroup[];
}

export function DocumentGenerator({ template, availableTokens = [] }: DocumentGeneratorProps) {
  const [tokens, setTokens] = useState<Record<string, any>>({});
  const [outputFormat, setOutputFormat] = useState<"html" | "pdf" | "docx">("html");
  const [generatedContent, setGeneratedContent] = useState<string>("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string>("");
  const [activeTab, setActiveTab] = useState("form");

  const handleTokenChange = (key: string, value: any) => {
    setTokens((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  const handleGenerate = async () => {
    setIsGenerating(true);
    setError("");

    try {
      const formData = new FormData();
      formData.append("template_id", template.id);
      formData.append("tokens", JSON.stringify(tokens));
      formData.append("output_format", outputFormat);

      const result = await generateDocument(formData);

      if (result.success && result.data) {
        setGeneratedContent(result.data.content);
        setActiveTab("preview");
      } else {
        setError(result.error || "Failed to generate document");
      }
    } catch (err) {
      setError("An error occurred while generating the document");
    } finally {
      setIsGenerating(false);
    }
  };

  const handleDownload = () => {
    if (!generatedContent) return;

    const blob = new Blob([generatedContent], {
      type: outputFormat === "html" ? "text/html" : "text/plain",
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${template.name.replace(/\s+/g, "_")}.${outputFormat}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getTokensByGroup = (groupName: string) => {
    const group = availableTokens.find((g) => g.name === groupName);
    return group?.tokens || [];
  };

  const renderTokenInput = (tokenKey: string, tokenLabel: string, tokenType: string) => {
    const value = tokens[tokenKey] || "";

    switch (tokenType) {
      case "date":
        return (
          <Input
            type="date"
            value={value}
            onChange={(e) => handleTokenChange(tokenKey, e.target.value)}
          />
        );
      case "number":
        return (
          <Input
            type="number"
            value={value}
            onChange={(e) => handleTokenChange(tokenKey, e.target.value)}
          />
        );
      case "boolean":
        return (
          <Select
            value={value.toString()}
            onValueChange={(val) => handleTokenChange(tokenKey, val === "true")}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="true">Yes</SelectItem>
              <SelectItem value="false">No</SelectItem>
            </SelectContent>
          </Select>
        );
      default:
        return (
          <Textarea
            value={value}
            onChange={(e) => handleTokenChange(tokenKey, e.target.value)}
            placeholder={`Enter ${tokenLabel.toLowerCase()}`}
            rows={2}
          />
        );
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <FileEdit className="h-5 w-5" />
                Generate Document
              </CardTitle>
              <p className="text-sm text-muted-foreground mt-1">
                Create a document from "{template.name}" template
              </p>
            </div>
            <Badge variant="outline">category</Badge>
          </div>
        </CardHeader>
      </Card>

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="form">Fill Data</TabsTrigger>
          <TabsTrigger value="preview">Preview</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        {/* Form Tab */}
        <TabsContent value="form" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Contact Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <User className="h-4 w-4" />
                  Contact Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {getTokensByGroup("Contact Information").map((token) => (
                  <div key={token.key}>
                    <Label htmlFor={token.key}>
                      {token.label}
                      {token.required && <span className="text-destructive ml-1">*</span>}
                    </Label>
                    {renderTokenInput(token.key, token.label, token.type)}
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Organization Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Building className="h-4 w-4" />
                  Organization Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {getTokensByGroup("Organization Information").map((token) => (
                  <div key={token.key}>
                    <Label htmlFor={token.key}>
                      {token.label}
                      {token.required && <span className="text-destructive ml-1">*</span>}
                    </Label>
                    {renderTokenInput(token.key, token.label, token.type)}
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Document Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Calendar className="h-4 w-4" />
                  Document Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {getTokensByGroup("Document Information").map((token) => (
                  <div key={token.key}>
                    <Label htmlFor={token.key}>
                      {token.label}
                      {token.required && <span className="text-destructive ml-1">*</span>}
                    </Label>
                    {renderTokenInput(token.key, token.label, token.type)}
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Custom Fields */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <FileText className="h-4 w-4" />
                  Custom Fields
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {getTokensByGroup("Custom Fields").map((token) => (
                  <div key={token.key}>
                    <Label htmlFor={token.key}>
                      {token.label}
                      {token.required && <span className="text-destructive ml-1">*</span>}
                    </Label>
                    {renderTokenInput(token.key, token.label, token.type)}
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Generate Button */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="output-format">Output Format</Label>
                  <Select
                    value={outputFormat}
                    onValueChange={(value: any) => setOutputFormat(value)}
                  >
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="html">HTML</SelectItem>
                      <SelectItem value="pdf">PDF</SelectItem>
                      <SelectItem value="docx">DOCX</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Button
                  onClick={handleGenerate}
                  disabled={isGenerating}
                  className="flex items-center gap-2"
                >
                  <FileText className="h-4 w-4" />
                  {isGenerating ? "Generating..." : "Generate Document"}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Preview Tab */}
        <TabsContent value="preview" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  Document Preview
                </CardTitle>
                {generatedContent && (
                  <Button onClick={handleDownload} className="flex items-center gap-2">
                    <Download className="h-4 w-4" />
                    Download
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent>
              {generatedContent ? (
                <div
                  className="min-h-[500px] p-6 border rounded-md bg-white prose prose-sm max-w-none"
                  dangerouslySetInnerHTML={{ __html: generatedContent }}
                />
              ) : (
                <div className="min-h-[500px] flex items-center justify-center text-muted-foreground">
                  <div className="text-center">
                    <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>Generate a document to see the preview</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Settings Tab */}
        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Generation Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="output-format-settings">Output Format</Label>
                <Select value={outputFormat} onValueChange={(value: any) => setOutputFormat(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="html">HTML - Web format</SelectItem>
                    <SelectItem value="pdf">PDF - Portable document</SelectItem>
                    <SelectItem value="docx">DOCX - Microsoft Word</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Template Information</Label>
                <div className="text-sm space-y-1">
                  <p>
                    <span className="font-medium">Name:</span> {template.name}
                  </p>
                  <p>{/* <span className="font-medium">Category:</span> {template.category} */}</p>
                  <p>
                    <span className="font-medium">Description:</span>{" "}
                    {/* {template.description || "No description"} */}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
