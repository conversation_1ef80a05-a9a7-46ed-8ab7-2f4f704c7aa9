import { Suspense } from "react";
import Link from "next/link";
import { notFound } from "next/navigation";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Edit, Copy, FileText, Trash2, Eye } from "lucide-react";
import { viewTemplate } from "../../../actions/view";
import { TemplateDetail } from "../../../components/TemplateDetail";

interface TemplateViewPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function TemplateViewPage({ params }: TemplateViewPageProps) {
  const { id } = await params;
  const result = await viewTemplate(id);

  if (!result.success) {
    notFound();
  }

  const template = result.data;

  if (!template) {
    notFound();
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="../list">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Templates
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{template.name}</h1>
            <p className="text-muted-foreground">Template Details</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" asChild>
            <Link href={`./preview`}>
              <Eye className="h-4 w-4 mr-2" />
              Preview
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href={`./generate`}>
              <FileText className="h-4 w-4 mr-2" />
              Generate Document
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href={`./edit`}>
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Link>
          </Button>
          <Button variant="outline">
            <Copy className="h-4 w-4 mr-2" />
            Duplicate
          </Button>
          <Button variant="outline" className="text-destructive hover:text-destructive">
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </div>
      </div>

      {/* Template Details */}
      <Suspense fallback={<div className="p-6">Loading template details...</div>}>
        <TemplateDetail template={template} />
      </Suspense>
    </div>
  );
}
