import { Suspense } from "react";
import Link from "next/link";
import { notFound } from "next/navigation";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Edit } from "lucide-react";
import { viewTemplate, getAvailableTokens } from "../../../actions/view";
import { TemplatePreview } from "../../../components/TemplatePreview";

interface TemplatePreviewPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function TemplatePreviewPage({ params }: TemplatePreviewPageProps) {
  // Get template data and available tokens in parallel
  const { id } = await params;
  const [templateResult, tokensResult] = await Promise.all([
    viewTemplate(id),
    getAvailableTokens(),
  ]);

  if (!templateResult.success) {
    notFound();
  }

  const template = templateResult.data;
  const availableTokens = tokensResult.success ? tokensResult.data : [];

  if (!template) {
    notFound();
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="outline" size="sm" asChild>
          <Link href="./view">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Template
          </Link>
        </Button>
        <div className="flex-1">
          <h1 className="text-3xl font-bold tracking-tight">Preview Template</h1>
          <p className="text-muted-foreground">
            Preview "{template.name}" with sample data or custom values
          </p>
        </div>
        <Button asChild>
          <Link href="./edit">
            <Edit className="h-4 w-4 mr-2" />
            Edit Template
          </Link>
        </Button>
      </div>

      {/* Template Preview */}
      <Suspense fallback={<div className="p-6">Loading preview...</div>}>
        <TemplatePreview template={template} availableTokens={availableTokens || []} />
      </Suspense>
    </div>
  );
}
