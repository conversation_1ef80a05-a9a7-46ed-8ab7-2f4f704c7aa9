"use server";

import { ProfileService } from "@/app/[lang]/protected/organization/(features)/profile/lib/services/ProfileService";
import { auth } from "@/lib/authentication/services/AuthenticationService";

/**
 * Get all active services for the current organization
 */
export async function getServices() {
  try {
    // Get current user profile
    const userProfile = await auth.getCurrentUserProfile();
    if (!userProfile?.organizationId) {
      return {
        success: false,
        message: "Organization context required",
        data: [],
      };
    }

    // Get services using ProfileService
    const services = await ProfileService.getServices();

    // Filter only active services
    const activeServices = services.filter((service) => service.status === "active");

    return {
      success: true,
      data: activeServices,
      message: "Services fetched successfully",
    };
  } catch (error) {
    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to fetch services",
      data: [],
    };
  }
}
