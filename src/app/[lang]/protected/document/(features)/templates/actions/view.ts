"use server";

import { DocumentTemplateService } from "../lib/services/DocumentTemplateService";
import { auth } from "@/lib/authentication/services/AuthenticationService";

/**
 * Get a document template by ID
 */
export async function viewTemplate(id: string) {
  try {
    const userProfile = await auth.getCurrentUserProfile();
    if (!userProfile?.organizationId) {
      return {
        success: false,
        message: "Organization context required",
      };
    }

    // Get template using service
    const templateService = new DocumentTemplateService();
    const result = await templateService.getTemplateById(id);

    if (!result.success) {
      return {
        success: false,
        message: result.message,
      };
    }

    // Verify template belongs to user's organization
    if (result.data?.organization_id !== userProfile.organizationId) {
      return {
        success: false,
        message: "Template not found",
      };
    }

    return {
      success: true,
      data: result.data || undefined,
      message: "Template fetched successfully",
    };
  } catch (error) {
    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to fetch template",
    };
  }
}

/**
 * Get available tokens for template editing
 */
export async function getAvailableTokens() {
  try {
    const templateService = new DocumentTemplateService();
    const tokens = templateService.getAvailableTokens();

    return {
      success: true,
      data: tokens,
      message: "Available tokens fetched successfully",
    };
  } catch (error) {
    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to fetch available tokens",
    };
  }
}
