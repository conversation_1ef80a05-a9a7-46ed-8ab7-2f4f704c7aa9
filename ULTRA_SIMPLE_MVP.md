# Ultra Simple MVP: Request Completed → Case File + Document

## Overview
**Absolute minimum**: When request status changes to "completed", directly create a case file and generate one document. No schema changes, no n8n, no new APIs.

## What We'll Build

### 1. Modify Existing Request Status Update
```typescript
// In src/app/[lang]/protected/request/actions/update.ts
export async function updateRequestStatus(id: string, status: RequestStatus) {
  // ... existing status update logic
  
  // NEW: When status becomes "completed", create case file + document
  if (status === "completed" && response.success) {
    try {
      await createCaseFileWithDocument(id);
    } catch (error) {
      // Log error but don't fail the status update
      console.error("Failed to create case file:", error);
    }
  }
  
  return response;
}
```

### 2. Simple Case File Creation Function
```typescript
// New function in the same file
async function createCaseFileWithDocument(requestId: string) {
  const userProfile = await auth.getCurrentUserProfile();
  if (!userProfile?.organizationId) return;

  // 1. Get request details
  const supabase = await createClient();
  const { data: request } = await supabase
    .from("requests")
    .select("*, contacts(*), services(*)")
    .eq("id", requestId)
    .single();

  if (!request) return;

  // 2. Create case file
  const { data: caseFile } = await supabase
    .from("case_files")
    .insert({
      organization_id: userProfile.organizationId,
      request_id: requestId,
      case_number: `CF-${Date.now()}`, // Simple case number
      status: "opening",
      primary_contact_id: request.primary_contact_id,
      service_id: request.service_id,
      created_by: userProfile.id,
    })
    .select()
    .single();

  if (!caseFile) return;

  // 3. Generate simple service agreement document
  await generateServiceAgreement(caseFile, request);
}
```

### 3. Simple Document Generation
```typescript
async function generateServiceAgreement(caseFile: any, request: any) {
  // Get a simple template (hardcoded for MVP)
  const supabase = await createClient();
  
  // Find any template with "service" or "agreement" in the name
  const { data: template } = await supabase
    .from("document_templates")
    .select("*")
    .eq("organization_id", caseFile.organization_id)
    .eq("is_active", true)
    .ilike("name", "%service%")
    .limit(1)
    .single();

  if (!template) {
    console.log("No service template found, skipping document generation");
    return;
  }

  // Simple token replacement
  let content = template.content;
  content = content.replace(/\{\{contact\.name\}\}/g, request.contacts?.name || "");
  content = content.replace(/\{\{case\.case_number\}\}/g, caseFile.case_number);
  content = content.replace(/\{\{date\.current\}\}/g, new Date().toLocaleDateString());
  content = content.replace(/\{\{organization\.name\}\}/g, "Your Organization");

  // Save as document attachment
  await supabase
    .from("document_attachments")
    .insert({
      organization_id: caseFile.organization_id,
      attached_to_type: "case_file",
      attached_to_id: caseFile.id,
      document_name: `Service Agreement - ${caseFile.case_number}`,
      file_path: `/generated/${caseFile.id}-service-agreement.html`,
      document_type: "HTML",
      file_size: content.length,
      attachment_type: "generated",
      uploaded_by: caseFile.created_by,
      metadata: {
        template_id: template.id,
        template_name: template.name,
        generated_content: content,
        generated_at: new Date().toISOString(),
        auto_generated: true,
      },
    });
}
```

## Implementation Steps

### Step 1: Add the Functions (30 minutes)
1. Open `src/app/[lang]/protected/request/actions/update.ts`
2. Add the two new functions at the bottom
3. Add the trigger in `updateRequestStatus`

### Step 2: Test It (15 minutes)
1. Create a request
2. Change status to "completed"
3. Check if case file was created
4. Check if document was generated

### Step 3: View the Results (15 minutes)
1. Go to case files list
2. Find the new case file
3. View the generated document

## What This Demonstrates

### User Experience
- "I mark a request as completed"
- "A case file is automatically created"
- "The case file has a service agreement document ready"

### Technical Proof
- Request → Case File automation works
- Document generation from templates works
- Integration between domains works
- No complex infrastructure needed

## Files to Modify

### Only ONE File
- `src/app/[lang]/protected/request/actions/update.ts`

### Changes Required
- Add 2 new functions (50 lines total)
- Add 1 trigger call (5 lines)
- Import statements (2 lines)

**Total: ~60 lines of code in 1 file**

## Success Criteria

1. ✅ Request status "completed" → Case file created
2. ✅ Case file has correct data from request
3. ✅ Service agreement document generated
4. ✅ Document visible in case file view
5. ✅ No errors in console

## What We're NOT Building

- ❌ No schema changes
- ❌ No n8n workflows
- ❌ No new API endpoints
- ❌ No complex template mappings
- ❌ No progress monitoring
- ❌ No email notifications
- ❌ No error handling UIs
- ❌ No contact linking (use existing case file structure)

## Why This Works

### Advantages
- **Minimal code**: Only ~60 lines
- **No infrastructure**: Uses existing systems
- **Immediate**: Works right after code change
- **Safe**: Doesn't break anything existing
- **Demonstrable**: Clear before/after behavior

### Limitations
- Only works with existing service templates
- Basic error handling
- No progress feedback
- No customization options

## Future Enhancements

After this works, we can add:
- Multiple templates
- Better error handling
- Progress notifications
- Template selection logic
- Contact document linking
- n8n workflows
- Complex template mappings

## Testing Plan

### Manual Test
1. Create a request with primary contact
2. Set status to "processing"
3. Set status to "completed"
4. Check case files list for new case file
5. Open case file and verify document exists
6. Open document and verify content has real data

### Expected Result
- New case file appears with status "opening"
- Case file contains generated service agreement
- Document content has replaced tokens with real data
- No errors in browser console

**This is the absolute minimum to prove the concept works!**
