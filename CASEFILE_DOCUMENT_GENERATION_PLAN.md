# Case File Document Generation Implementation Plan

## Overview
When a request is completed (approved), automatically create a case file and generate all necessary documents for each contact based on templates, fully preparing the case file before opening.

## Current State Analysis

### Existing Components
- ✅ Request management system
- ✅ Template system with document generation
- ✅ Case file management
- ✅ Contact management
- ✅ Document attachment system

### Missing Components
- ❌ Automatic case file creation from approved requests
- ❌ Bulk document generation for multiple contacts
- ❌ Template-to-contact mapping system
- ❌ Document linking to case files with contact association
- ❌ Case file preparation workflow

## Implementation Plan

### Phase 1: Database Schema Updates

#### 1.1 Case File Templates Association
```sql
-- New table to define which templates should be generated for case files
CREATE TABLE case_file_template_mappings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL REFERENCES organizations(id),
  service_id UUID REFERENCES services(id),
  template_id UUID NOT NULL REFERENCES document_templates(id),
  contact_role TEXT NOT NULL, -- 'primary_contact', 'secondary_contact', 'all_contacts'
  is_required BOOLEAN DEFAULT true,
  generation_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 1.2 Document Generation Jobs
```sql
-- Track document generation progress
CREATE TABLE document_generation_jobs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL REFERENCES organizations(id),
  case_file_id UUID NOT NULL REFERENCES case_files(id),
  request_id UUID REFERENCES requests(id),
  status TEXT NOT NULL DEFAULT 'pending', -- 'pending', 'in_progress', 'completed', 'failed'
  total_documents INTEGER DEFAULT 0,
  completed_documents INTEGER DEFAULT 0,
  failed_documents INTEGER DEFAULT 0,
  error_details JSONB,
  started_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 1.3 Enhanced Document Attachments
```sql
-- Add contact association to document attachments
ALTER TABLE document_attachments 
ADD COLUMN contact_id UUID REFERENCES contacts(id),
ADD COLUMN generation_job_id UUID REFERENCES document_generation_jobs(id),
ADD COLUMN template_mapping_id UUID REFERENCES case_file_template_mappings(id);
```

### Phase 2: Core Services

#### 2.1 Case File Document Generator Service
```typescript
class CaseFileDocumentGeneratorService {
  // Main orchestrator method
  async generateCaseFileDocuments(requestId: string): Promise<ServiceResponse<CaseFile>>
  
  // Create case file from approved request
  async createCaseFileFromRequest(request: Request): Promise<ServiceResponse<CaseFile>>
  
  // Get templates that should be generated for this case
  async getRequiredTemplates(serviceId: string, organizationId: string): Promise<TemplateMapping[]>
  
  // Generate documents for all contacts
  async generateDocumentsForContacts(
    caseFile: CaseFile, 
    contacts: Contact[], 
    templateMappings: TemplateMapping[]
  ): Promise<ServiceResponse<DocumentGenerationJob>>
  
  // Generate single document for contact
  async generateDocumentForContact(
    template: DocumentTemplate,
    contact: Contact,
    caseFile: CaseFile,
    jobId: string
  ): Promise<ServiceResponse<DocumentAttachment>>
}
```

#### 2.2 Template Mapping Service
```typescript
class TemplateMappingService {
  // Manage which templates are required for case files
  async createTemplateMapping(mapping: CreateTemplateMappingData): Promise<ServiceResponse<TemplateMapping>>
  async getTemplateMappingsForService(serviceId: string): Promise<ServiceResponse<TemplateMapping[]>>
  async updateTemplateMapping(id: string, updates: UpdateTemplateMappingData): Promise<ServiceResponse<TemplateMapping>>
  async deleteTemplateMapping(id: string): Promise<ServiceResponse<void>>
}
```

### Phase 3: n8n Workflow Integration

#### 3.1 Request Completion Webhook
```typescript
// In request approval action - trigger n8n webhook
async function approveRequest(requestId: string) {
  // ... existing approval logic

  if (result.success) {
    // Trigger n8n workflow via webhook
    await fetch(`${process.env.N8N_WEBHOOK_URL}/request-approved`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        requestId,
        organizationId: userProfile.organizationId,
        timestamp: new Date().toISOString()
      })
    });
  }
}
```

#### 3.2 n8n Workflow Definition
```yaml
# n8n Workflow: Request Approved → Case File Generation
workflow_name: "case-file-document-generation"
trigger:
  type: "webhook"
  path: "/request-approved"

nodes:
  1. webhook_trigger:
      type: "webhook"

  2. get_request_details:
      type: "http_request"
      url: "${API_BASE_URL}/api/requests/${requestId}"

  3. create_case_file:
      type: "http_request"
      url: "${API_BASE_URL}/api/case-files/create-from-request"
      method: "POST"
      body: { requestId: "${requestId}" }

  4. get_template_mappings:
      type: "http_request"
      url: "${API_BASE_URL}/api/template-mappings/service/${serviceId}"

  5. start_document_generation:
      type: "http_request"
      url: "${API_BASE_URL}/api/document-generation/start"
      method: "POST"
      body: {
        caseFileId: "${caseFileId}",
        templateMappings: "${templateMappings}",
        contacts: "${contacts}"
      }

  6. monitor_generation_progress:
      type: "loop"
      condition: "generation_status !== 'completed'"
      delay: 30 # seconds

  7. send_completion_notification:
      type: "email" # or webhook to notification service
      condition: "generation_status === 'completed'"

  8. send_failure_notification:
      type: "email" # or webhook to notification service
      condition: "generation_status === 'failed'"
```

#### 3.3 API Endpoints for n8n Integration
```typescript
// New API endpoints to support n8n workflow
export const caseFileGenerationRoutes = {
  // Create case file from request
  'POST /api/case-files/create-from-request': createCaseFileFromRequest,

  // Get template mappings for service
  'GET /api/template-mappings/service/:serviceId': getTemplateMappingsForService,

  // Start document generation job
  'POST /api/document-generation/start': startDocumentGeneration,

  // Get generation job status
  'GET /api/document-generation/status/:jobId': getGenerationJobStatus,

  // Retry failed document generation
  'POST /api/document-generation/retry/:jobId': retryDocumentGeneration,
}
```

### Phase 4: User Interface

#### 4.1 Template Mapping Management
- **Page**: `/protected/admin/template-mappings`
- **Features**:
  - Configure which templates are required for each service
  - Set contact roles (primary, secondary, all)
  - Define generation order
  - Mark templates as required/optional

#### 4.2 Case File Document Status
- **Enhancement**: Case file view page
- **Features**:
  - Show document generation progress
  - Display generated documents by contact
  - Retry failed document generation
  - Preview generated documents

#### 4.3 Document Generation Monitoring
- **Page**: `/protected/admin/document-generation-jobs`
- **Features**:
  - Monitor ongoing generation jobs
  - View generation statistics
  - Troubleshoot failed generations

### Phase 5: Configuration & Settings

#### 5.1 Service Configuration
```typescript
// Add to service settings
interface ServiceSettings {
  // ... existing settings
  auto_generate_documents: boolean;
  required_templates: string[]; // template IDs
  document_generation_delay: number; // minutes to wait before generation
}
```

#### 5.2 Organization Preferences
```typescript
// Add to organization settings
interface OrganizationDocumentSettings {
  enable_auto_case_file_creation: boolean;
  enable_bulk_document_generation: boolean;
  default_document_language: 'fr' | 'en';
  notification_preferences: {
    notify_on_generation_complete: boolean;
    notify_on_generation_failure: boolean;
  };
}
```

## Implementation Sequence

### Week 1: Foundation
1. Database schema updates
2. Core service interfaces
3. Basic template mapping CRUD
4. API endpoints for n8n integration

### Week 2: Document Generation
1. Case file creation from requests API
2. Document generation service
3. Template-contact mapping logic
4. Generation job status tracking

### Week 3: n8n Workflow Integration
1. n8n workflow creation and testing
2. Webhook integration in request approval
3. Error handling and retry mechanisms
4. Progress monitoring and notifications

### Week 4: User Interface
1. Template mapping management UI
2. Case file document status display
3. Generation monitoring dashboard
4. n8n workflow status integration

### Week 5: Testing & Polish
1. End-to-end testing with n8n
2. Performance optimization
3. n8n workflow documentation
4. User training and documentation

## Technical Considerations

### n8n Workflow Benefits
- **Visual workflow management**: Easy to understand and modify
- **Built-in error handling**: Automatic retries and error notifications
- **Monitoring and logging**: Built-in execution history and debugging
- **Flexibility**: Easy to add new steps or modify workflow logic
- **Scalability**: n8n handles workflow execution and queuing
- **Maintenance**: Non-developers can modify workflows

### Performance
- n8n handles workflow orchestration and queuing
- API endpoints optimized for bulk operations
- Progress tracking through generation jobs
- Configurable timeouts and retry policies

### Error Handling
- n8n built-in error handling and retries
- Detailed error logging in generation jobs
- Graceful degradation for partial failures
- Notification system for failures

### Scalability
- n8n workflow engine handles scaling
- API endpoints designed for concurrent requests
- Database optimized for bulk document operations
- Resource usage monitoring through n8n

### User Experience
- Real-time progress updates via API
- n8n workflow status integration
- Clear status indicators in UI
- Easy retry mechanisms through n8n

## Success Criteria

1. ✅ Approved requests automatically create case files
2. ✅ All required documents are generated for each contact
3. ✅ Documents are properly linked to case files and contacts
4. ✅ Generation process is reliable and monitorable
5. ✅ Failed generations can be easily retried
6. ✅ Case files are fully prepared before opening

## Questions for Review

1. **Template Selection**: Should template mappings be per service or more granular?
2. **Contact Roles**: What contact roles should we support beyond primary/secondary?
3. **Generation Timing**: Should generation be immediate or delayed?
4. **Failure Handling**: How should we handle partial generation failures?
5. **Notifications**: Who should be notified when generation completes/fails?
6. **Document Naming**: How should generated documents be named?
7. **Version Control**: Should we track document generation versions?

## Next Steps

1. Review this plan together
2. Refine requirements based on feedback
3. Prioritize features for MVP
4. Begin implementation with database schema
5. Create detailed technical specifications

## Detailed API Specifications

### API Endpoints for n8n Integration

#### 1. Create Case File from Request
```typescript
POST /api/case-files/create-from-request
Content-Type: application/json

Request Body:
{
  "requestId": "uuid",
  "organizationId": "uuid"
}

Response:
{
  "success": true,
  "data": {
    "caseFileId": "uuid",
    "caseNumber": "CF-2024-001",
    "status": "preparing",
    "contacts": [
      {
        "id": "uuid",
        "name": "John Doe",
        "role": "primary_contact",
        "email": { "personal": "<EMAIL>" }
      }
    ],
    "serviceId": "uuid"
  }
}
```

#### 2. Get Template Mappings for Service
```typescript
GET /api/template-mappings/service/:serviceId
Authorization: Bearer <token>

Response:
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "templateId": "uuid",
      "templateName": "Service Agreement",
      "contactRole": "primary_contact",
      "isRequired": true,
      "generationOrder": 1
    },
    {
      "id": "uuid",
      "templateId": "uuid",
      "templateName": "Contact Information Form",
      "contactRole": "all_contacts",
      "isRequired": true,
      "generationOrder": 2
    }
  ]
}
```

#### 3. Start Document Generation Job
```typescript
POST /api/document-generation/start
Content-Type: application/json

Request Body:
{
  "caseFileId": "uuid",
  "requestId": "uuid",
  "templateMappings": [
    {
      "templateId": "uuid",
      "contactRole": "primary_contact",
      "isRequired": true
    }
  ],
  "contacts": [
    {
      "id": "uuid",
      "role": "primary_contact"
    }
  ]
}

Response:
{
  "success": true,
  "data": {
    "jobId": "uuid",
    "status": "pending",
    "totalDocuments": 4,
    "estimatedDuration": "2-3 minutes"
  }
}
```

#### 4. Get Generation Job Status
```typescript
GET /api/document-generation/status/:jobId
Authorization: Bearer <token>

Response:
{
  "success": true,
  "data": {
    "jobId": "uuid",
    "status": "in_progress", // pending, in_progress, completed, failed
    "totalDocuments": 4,
    "completedDocuments": 2,
    "failedDocuments": 0,
    "progress": 50,
    "estimatedTimeRemaining": "1-2 minutes",
    "generatedDocuments": [
      {
        "documentId": "uuid",
        "documentName": "Service Agreement - John Doe",
        "contactId": "uuid",
        "contactName": "John Doe",
        "templateName": "Service Agreement",
        "status": "completed",
        "generatedAt": "2024-01-15T10:30:00Z"
      }
    ],
    "errors": []
  }
}
```

#### 5. Retry Failed Document Generation
```typescript
POST /api/document-generation/retry/:jobId
Content-Type: application/json

Request Body:
{
  "retryFailedOnly": true
}

Response:
{
  "success": true,
  "data": {
    "jobId": "uuid",
    "status": "in_progress",
    "retriedDocuments": 1
  }
}
```

## n8n Workflow Configuration

### Environment Variables
```env
# n8n Configuration
N8N_WEBHOOK_URL=http://n8n:5678/webhook
API_BASE_URL=http://app:3000
N8N_API_KEY=your-n8n-api-key

# Notification Settings
NOTIFICATION_EMAIL_FROM=<EMAIL>
NOTIFICATION_EMAIL_TO=<EMAIL>
```

### Webhook Security
```typescript
// Webhook authentication in n8n
const webhookAuth = {
  type: "headerAuth",
  headerName: "X-N8N-Webhook-Secret",
  secret: process.env.N8N_WEBHOOK_SECRET
};
```
