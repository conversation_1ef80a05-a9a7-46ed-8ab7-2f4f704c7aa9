# MVP: Case File Document Generation

## Overview
**Simple MVP**: When a request status changes to "completed", automatically create a case file and generate one basic document to demonstrate the concept.

## MVP Scope (Minimal Viable Product)

### What We'll Build
1. **Trigger**: Request status changes to "completed"
2. **Action**: Create case file from request
3. **Generate**: ONE document (Service Agreement template)
4. **Link**: Document to case file and primary contact
5. **Show**: Basic success/failure feedback

### What We WON'T Build (for MVP)
- ❌ Multiple templates per service
- ❌ Complex template mappings
- ❌ Background job processing
- ❌ Progress monitoring
- ❌ Retry mechanisms
- ❌ Email notifications
- ❌ Complex UI management

## Implementation Plan

### Phase 1: Database Changes (Minimal)
```sql
-- Just add contact_id to existing document_attachments table
ALTER TABLE document_attachments 
ADD COLUMN contact_id UUID REFERENCES contacts(id);

-- Add index for performance
CREATE INDEX idx_document_attachments_contact_id ON document_attachments(contact_id);
```

### Phase 2: Simple Service Method
```typescript
class CaseFileGenerationService {
  /**
   * Create case file and generate basic document when request is completed
   */
  async createCaseFileWithDocument(requestId: string): Promise<ServiceResponse<CaseFile>> {
    try {
      // 1. Get request details
      const request = await this.getRequest(requestId);
      
      // 2. Create case file from request
      const caseFile = await this.createCaseFile(request);
      
      // 3. Generate ONE document (Service Agreement)
      const document = await this.generateServiceAgreement(caseFile, request.primary_contact);
      
      // 4. Link document to case file and contact
      await this.linkDocument(document.id, caseFile.id, request.primary_contact.id);
      
      return successResponse(caseFile, "Case file created with document");
    } catch (error) {
      return errorResponse("Failed to create case file", error.message);
    }
  }
}
```

### Phase 3: Simple n8n Workflow
```yaml
# Minimal n8n workflow
workflow_name: "mvp-case-file-generation"

nodes:
  1. webhook_trigger:
      path: "/request-completed"
      
  2. create_case_file_with_document:
      type: "http_request"
      url: "${API_BASE_URL}/api/case-files/create-with-document"
      method: "POST"
      body: { requestId: "${requestId}" }
      
  3. webhook_response:
      type: "respond_to_webhook"
      body: { success: true, message: "Case file created" }
```

### Phase 4: Simple API Endpoint
```typescript
// Single API endpoint for MVP
export async function POST(request: Request) {
  try {
    const { requestId } = await request.json();
    
    const result = await CaseFileGenerationService
      .getInstance()
      .createCaseFileWithDocument(requestId);
    
    return NextResponse.json(result);
  } catch (error) {
    return NextResponse.json(
      { success: false, error: error.message },
      { status: 500 }
    );
  }
}
```

### Phase 5: Simple Integration Hook
```typescript
// In request status update action
export async function updateRequestStatus(id: string, status: RequestStatus) {
  // ... existing status update logic
  
  // MVP: Simple trigger when status becomes "completed"
  if (status === "completed" && result.success) {
    try {
      // Trigger n8n workflow
      await fetch(`${process.env.N8N_WEBHOOK_URL}/request-completed`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ requestId: id })
      });
    } catch (error) {
      // Log error but don't fail the status update
      console.error("Failed to trigger case file generation:", error);
    }
  }
  
  return result;
}
```

## MVP Features

### 1. Automatic Case File Creation
- ✅ Create case file when request completed
- ✅ Copy basic info from request to case file
- ✅ Set case file status to "opening"

### 2. Single Document Generation
- ✅ Use existing "Service Agreement" template
- ✅ Replace tokens with request/contact data
- ✅ Save as document attachment

### 3. Basic Linking
- ✅ Link document to case file
- ✅ Link document to primary contact
- ✅ Store generation metadata

### 4. Simple Feedback
- ✅ Success/failure response
- ✅ Basic error logging
- ✅ Case file shows generated document

## Implementation Steps

### Week 1: Foundation
1. Add `contact_id` column to `document_attachments`
2. Create simple `CaseFileGenerationService`
3. Create single API endpoint

### Week 2: Integration
1. Create minimal n8n workflow
2. Add trigger to request status update
3. Test end-to-end flow

### Week 3: Polish
1. Add basic error handling
2. Update case file view to show generated documents
3. Test with real data

## Success Criteria (MVP)

1. ✅ Request status "completed" → Case file created
2. ✅ Service Agreement document generated automatically
3. ✅ Document linked to case file and contact
4. ✅ Case file shows the generated document
5. ✅ Basic error handling works

## What This Demonstrates

### For Users
- "When I complete a request, a case file is automatically created"
- "The case file already has the service agreement ready"
- "I can see which document belongs to which contact"

### For Development
- Proves the concept works
- Shows integration between requests, case files, and documents
- Demonstrates n8n workflow capability
- Provides foundation for future enhancements

## Future Enhancements (Post-MVP)

After MVP is working, we can add:
- Multiple templates per service
- Template mapping configuration
- Progress monitoring
- Email notifications
- Retry mechanisms
- Complex document generation rules

## Technical Decisions for MVP

### Why This Approach?
- **Simple**: Minimal code changes
- **Demonstrable**: Clear before/after behavior
- **Extensible**: Easy to add features later
- **Low Risk**: Doesn't break existing functionality
- **Fast**: Can be built in 2-3 weeks

### What We're Avoiding
- Complex template mapping systems
- Background job queues
- Progress monitoring UIs
- Advanced error handling
- Multiple document types

## MVP Testing Plan

### Test Scenarios
1. **Happy Path**: Request completed → Case file + document created
2. **Error Handling**: Template missing → Graceful failure
3. **Data Integrity**: Document properly linked to case file and contact
4. **UI Integration**: Case file view shows generated document

### Acceptance Criteria
- [ ] Request status change to "completed" triggers workflow
- [ ] Case file is created with correct data from request
- [ ] Service Agreement document is generated with real data
- [ ] Document appears in case file view
- [ ] Contact association is visible
- [ ] Error scenarios don't break request status updates

This MVP will prove the concept works and provide a solid foundation for future enhancements!
