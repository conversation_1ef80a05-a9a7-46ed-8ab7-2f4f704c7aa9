{"name": "Case File Document Generation", "nodes": [{"parameters": {"httpMethod": "POST", "path": "request-approved", "responseMode": "responseNode", "options": {}}, "id": "webhook-trigger", "name": "Request Approved Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "case-file-generation"}, {"parameters": {"url": "={{ $env.API_BASE_URL }}/api/requests/{{ $json.requestId }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpHeaderAuth", "options": {}}, "id": "get-request-details", "name": "Get Request Details", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [460, 300], "credentials": {"httpHeaderAuth": {"id": "api-auth", "name": "API Authentication"}}}, {"parameters": {"url": "={{ $env.API_BASE_URL }}/api/case-files/create-from-request", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"requestId\": \"{{ $('Request Approved Webhook').first().json.requestId }}\",\n  \"organizationId\": \"{{ $('Request Approved Webhook').first().json.organizationId }}\"\n}", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpHeaderAuth", "options": {}}, "id": "create-case-file", "name": "Create Case File", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [680, 300], "credentials": {"httpHeaderAuth": {"id": "api-auth", "name": "API Authentication"}}}, {"parameters": {"url": "={{ $env.API_BASE_URL }}/api/template-mappings/service/{{ $('Get Request Details').first().json.data.service_id }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpHeaderAuth", "options": {}}, "id": "get-template-mappings", "name": "Get Template Mappings", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [900, 300], "credentials": {"httpHeaderAuth": {"id": "api-auth", "name": "API Authentication"}}}, {"parameters": {"url": "={{ $env.API_BASE_URL }}/api/document-generation/start", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"caseFileId\": \"{{ $('Create Case File').first().json.data.caseFileId }}\",\n  \"requestId\": \"{{ $('Request Approved Webhook').first().json.requestId }}\",\n  \"templateMappings\": {{ $('Get Template Mappings').first().json.data }},\n  \"contacts\": {{ $('Create Case File').first().json.data.contacts }}\n}", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpHeaderAuth", "options": {}}, "id": "start-generation", "name": "Start Document Generation", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1120, 300], "credentials": {"httpHeaderAuth": {"id": "api-auth", "name": "API Authentication"}}}, {"parameters": {"amount": 30, "unit": "seconds"}, "id": "wait-30-seconds", "name": "Wait 30 Seconds", "type": "n8n-nodes-base.wait", "typeVersion": 1, "position": [1340, 300]}, {"parameters": {"url": "={{ $env.API_BASE_URL }}/api/document-generation/status/{{ $('Start Document Generation').first().json.data.jobId }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpHeaderAuth", "options": {}}, "id": "check-status", "name": "Check Generation Status", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1560, 300], "credentials": {"httpHeaderAuth": {"id": "api-auth", "name": "API Authentication"}}}, {"parameters": {"conditions": {"string": [{"value1": "={{ $json.data.status }}", "operation": "notEqual", "value2": "completed"}, {"value1": "={{ $json.data.status }}", "operation": "notEqual", "value2": "failed"}]}, "combineOperation": "all"}, "id": "check-if-still-running", "name": "Still Running?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1780, 300]}, {"parameters": {"conditions": {"string": [{"value1": "={{ $('Check Generation Status').first().json.data.status }}", "value2": "completed"}]}}, "id": "check-if-successful", "name": "Generation Successful?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [2000, 200]}, {"parameters": {"fromEmail": "={{ $env.NOTIFICATION_EMAIL_FROM }}", "toEmail": "={{ $env.NOTIFICATION_EMAIL_TO }}", "subject": "Case File Documents Generated Successfully", "text": "=Case File {{ $('Create Case File').first().json.data.caseNumber }} has been created and all documents have been generated successfully.\n\nGenerated Documents: {{ $('Check Generation Status').first().json.data.completedDocuments }}\nCase File ID: {{ $('Create Case File').first().json.data.caseFileId }}\n\nThe case file is now ready for review."}, "id": "send-success-email", "name": "Send Success Notification", "type": "n8n-nodes-base.emailSend", "typeVersion": 2, "position": [2220, 120], "credentials": {"smtp": {"id": "email-smtp", "name": "Email SMTP"}}}, {"parameters": {"fromEmail": "={{ $env.NOTIFICATION_EMAIL_FROM }}", "toEmail": "={{ $env.NOTIFICATION_EMAIL_TO }}", "subject": "Case File Document Generation Failed", "text": "=Case File {{ $('Create Case File').first().json.data.caseNumber }} document generation has failed or completed with errors.\n\nCompleted Documents: {{ $('Check Generation Status').first().json.data.completedDocuments }}\nFailed Documents: {{ $('Check Generation Status').first().json.data.failedDocuments }}\nCase File ID: {{ $('Create Case File').first().json.data.caseFileId }}\n\nManual review is required."}, "id": "send-failure-email", "name": "Send Failure Notification", "type": "n8n-nodes-base.emailSend", "typeVersion": 2, "position": [2220, 280], "credentials": {"smtp": {"id": "email-smtp", "name": "Email SMTP"}}}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": true,\n  \"message\": \"Case file document generation workflow started\",\n  \"jobId\": \"{{ $('Start Document Generation').first().json.data.jobId }}\",\n  \"caseFileId\": \"{{ $('Create Case File').first().json.data.caseFileId }}\"\n}"}, "id": "webhook-response", "name": "Webhook Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1340, 500]}], "connections": {"Request Approved Webhook": {"main": [[{"node": "Get Request Details", "type": "main", "index": 0}]]}, "Get Request Details": {"main": [[{"node": "Create Case File", "type": "main", "index": 0}]]}, "Create Case File": {"main": [[{"node": "Get Template Mappings", "type": "main", "index": 0}]]}, "Get Template Mappings": {"main": [[{"node": "Start Document Generation", "type": "main", "index": 0}]]}, "Start Document Generation": {"main": [[{"node": "Wait 30 Seconds", "type": "main", "index": 0}, {"node": "Webhook Response", "type": "main", "index": 0}]]}, "Wait 30 Seconds": {"main": [[{"node": "Check Generation Status", "type": "main", "index": 0}]]}, "Check Generation Status": {"main": [[{"node": "Still Running?", "type": "main", "index": 0}]]}, "Still Running?": {"main": [[{"node": "Wait 30 Seconds", "type": "main", "index": 0}], [{"node": "Generation Successful?", "type": "main", "index": 0}]]}, "Generation Successful?": {"main": [[{"node": "Send Success Notification", "type": "main", "index": 0}], [{"node": "Send Failure Notification", "type": "main", "index": 0}]]}}, "active": true, "settings": {"timezone": "America/Toronto"}, "versionId": "1"}