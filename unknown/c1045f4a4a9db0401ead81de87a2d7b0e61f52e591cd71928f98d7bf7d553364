import { Database } from "@/lib/types/database.types";

/**
 * Appointment interface from database
 */
export type Appointment = Database["public"]["Tables"]["appointments"]["Row"];

/**
 * Appointment Insert interface for creating new appointments
 */
export type AppointmentInsert = Database["public"]["Tables"]["appointments"]["Insert"];

/**
 * Appointment Update interface for updating existing appointments
 */
export type AppointmentUpdate = Database["public"]["Tables"]["appointments"]["Update"];

/**
 * Appointment Status enum
 */
export type AppointmentStatus = Database["public"]["Enums"]["appointment_status"];

/**
 * Extended appointment interface with computed fields and related data
 */
export interface AppointmentWithDetails extends Appointment {
  // Computed fields
  duration_display?: string;
  date_display?: string;
  time_display?: string;
  status_display?: string;

  // Related data
  services?: {
    id: string;
    name: string;
  } | null;

  appointment_assignments?: Array<{
    id: string;
    employees?: {
      id: string;
      first_name: string;
      last_name: string;
      job_title?: string | null;
    } | null;
  }>;

  appointment_rooms?: Array<{
    id: string;
    rooms?: {
      id: string;
      name: string;
      locations?: {
        id: string;
        name: string;
      } | null;
    } | null;
  }>;
}

/**
 * Appointment filters for listing
 */
export interface AppointmentFilters {
  status?: AppointmentStatus;
  date?: string;
  search?: string;
  page?: number;
  limit?: number;
}

/**
 * Appointment list response
 */
export interface AppointmentListResponse {
  appointments: AppointmentWithDetails[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

/**
 * Calendar event interface for ScheduleCalendar component
 */
export interface AppointmentCalendarEvent {
  id: string;
  title: string;
  start: Date;
  end: Date;
  type: AppointmentStatus;
  description?: string;
}

/**
 * Form data interface for appointment creation/editing
 */
export interface AppointmentFormData {
  title: string;
  description?: string;
  appointment_date: string;
  start_time: string;
  end_time: string;
  status: AppointmentStatus;
}

/**
 * Conflict check interface
 */
export interface AppointmentConflict {
  hasConflict: boolean;
  conflictingAppointments: Appointment[];
  message?: string;
}

/**
 * Status transition interface
 */
export interface StatusTransition {
  from: AppointmentStatus;
  to: AppointmentStatus;
  allowed: boolean;
  reason?: string;
}
