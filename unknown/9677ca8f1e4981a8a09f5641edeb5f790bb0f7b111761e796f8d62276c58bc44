import { createClient } from "@supabase/supabase-js";
import dotenv from "dotenv";

// Parse command line arguments
const args = process.argv.slice(2);
const helpArg = args.includes('--help') || args.includes('-h');

if (helpArg) {
  console.log(`
Usage: ts-node setup-bucket.ts [options]

Options:
  --env=<environment>  Specify which environment file to use:
                       'local' - Use .env.local (default)
                       'cloud' - Use .env
  --help, -h           Show this help message

Examples:
  ts-node setup-bucket.ts                 # Uses .env.local
  ts-node setup-bucket.ts --env=cloud     # Uses .env
  `);
  process.exit(0);
}

const envArg = args.find(arg => arg.startsWith('--env='));
const envFile = envArg ? envArg.split('=')[1] : 'local';

// Load environment variables from the appropriate file
const envPath = envFile === 'cloud' ? '.env' : '.env.local';
console.log(`Using environment file: ${envPath}`);
dotenv.config({ path: envPath });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error("Missing environment variables:");
  console.error("- NEXT_PUBLIC_SUPABASE_URL:", supabaseUrl ? "✓" : "✗");
  console.error("- SUPABASE_SERVICE_ROLE_KEY:", supabaseServiceKey ? "✓" : "✗");
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

// Storage bucket configuration
const BUCKET_CONFIG = {
  name: 'document-attachments',
  public: false,
  allowedMimeTypes: [
    // Documents
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'text/plain',
    'text/csv',
    // Images
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'image/svg+xml',
    // Archives
    'application/zip',
    'application/x-rar-compressed',
    'application/x-7z-compressed',
  ],
  fileSizeLimit: 50 * 1024 * 1024, // 50MB
};

// RLS Policies SQL
const RLS_POLICIES = [
  {
    name: 'Allow authenticated uploads',
    sql: `
      CREATE POLICY "Allow authenticated uploads" ON storage.objects
      FOR INSERT TO authenticated
      WITH CHECK (bucket_id = '${BUCKET_CONFIG.name}');
    `,
  },
  {
    name: 'Allow organization file access',
    sql: `
      CREATE POLICY "Allow organization file access" ON storage.objects
      FOR SELECT TO authenticated
      USING (
        bucket_id = '${BUCKET_CONFIG.name}' AND
        (storage.foldername(name))[1] = (
          SELECT organization_id::text 
          FROM auth.users 
          WHERE auth.users.id = auth.uid()
        )
      );
    `,
  },
  {
    name: 'Allow organization file deletion',
    sql: `
      CREATE POLICY "Allow organization file deletion" ON storage.objects
      FOR DELETE TO authenticated
      USING (
        bucket_id = '${BUCKET_CONFIG.name}' AND
        (storage.foldername(name))[1] = (
          SELECT organization_id::text 
          FROM auth.users 
          WHERE auth.users.id = auth.uid()
        )
      );
    `,
  },
  {
    name: 'Allow organization file updates',
    sql: `
      CREATE POLICY "Allow organization file updates" ON storage.objects
      FOR UPDATE TO authenticated
      USING (
        bucket_id = '${BUCKET_CONFIG.name}' AND
        (storage.foldername(name))[1] = (
          SELECT organization_id::text 
          FROM auth.users 
          WHERE auth.users.id = auth.uid()
        )
      );
    `,
  },
];

/**
 * Check if bucket exists
 */
async function checkBucketExists(): Promise<boolean> {
  try {
    const { data, error } = await supabase.storage.getBucket(BUCKET_CONFIG.name);
    if (error) {
      if (error.message.includes('not found')) {
        return false;
      }
      throw error;
    }
    return !!data;
  } catch (error) {
    console.error('Error checking bucket existence:', error);
    return false;
  }
}

/**
 * Create storage bucket
 */
async function createBucket(): Promise<boolean> {
  try {
    console.log(`📁 Creating bucket: ${BUCKET_CONFIG.name}...`);
    
    const { data, error } = await supabase.storage.createBucket(BUCKET_CONFIG.name, {
      public: BUCKET_CONFIG.public,
      allowedMimeTypes: BUCKET_CONFIG.allowedMimeTypes,
      fileSizeLimit: BUCKET_CONFIG.fileSizeLimit,
    });

    if (error) {
      console.error('❌ Error creating bucket:', error);
      return false;
    }

    console.log('✅ Bucket created successfully:', data);
    return true;
  } catch (error) {
    console.error('❌ Error creating bucket:', error);
    return false;
  }
}

/**
 * Update bucket configuration
 */
async function updateBucketConfig(): Promise<boolean> {
  try {
    console.log(`🔧 Updating bucket configuration: ${BUCKET_CONFIG.name}...`);
    
    const { data, error } = await supabase.storage.updateBucket(BUCKET_CONFIG.name, {
      public: BUCKET_CONFIG.public,
      allowedMimeTypes: BUCKET_CONFIG.allowedMimeTypes,
      fileSizeLimit: BUCKET_CONFIG.fileSizeLimit,
    });

    if (error) {
      console.error('❌ Error updating bucket:', error);
      return false;
    }

    console.log('✅ Bucket configuration updated successfully');
    return true;
  } catch (error) {
    console.error('❌ Error updating bucket configuration:', error);
    return false;
  }
}

/**
 * Execute SQL policy
 */
async function executeSQLPolicy(policy: { name: string; sql: string }): Promise<boolean> {
  try {
    console.log(`🔐 Creating RLS policy: ${policy.name}...`);
    
    // First, try to drop the policy if it exists
    const dropQuery = `DROP POLICY IF EXISTS "${policy.name}" ON storage.objects;`;
    
    try {
      const { error: dropError } = await supabase.rpc('exec_sql', { sql: dropQuery });
      if (dropError) {
        console.warn(`⚠️  Could not drop existing policy "${policy.name}":`, dropError.message);
      }
    } catch (dropErr) {
      // Ignore drop errors - policy might not exist
      console.log(`ℹ️  Policy "${policy.name}" does not exist yet, creating new...`);
    }
    
    // Then create the new policy
    const { error } = await supabase.rpc('exec_sql', { sql: policy.sql });
    
    if (error) {
      console.error(`❌ Error creating policy "${policy.name}":`, error);
      return false;
    }

    console.log(`✅ RLS policy "${policy.name}" created successfully`);
    return true;
  } catch (error) {
    console.error(`❌ Error executing policy "${policy.name}":`, error);
    return false;
  }
}

/**
 * Setup RLS policies
 */
async function setupRLSPolicies(): Promise<boolean> {
  console.log('🔒 Setting up RLS policies...');
  
  let allSuccess = true;
  
  for (const policy of RLS_POLICIES) {
    const success = await executeSQLPolicy(policy);
    if (!success) {
      allSuccess = false;
      console.error(`❌ Failed to create policy: ${policy.name}`);
    }
  }
  
  if (allSuccess) {
    console.log('✅ All RLS policies created successfully');
  } else {
    console.error('⚠️  Some RLS policies failed to create');
    console.log('\n📝 Note: You may need to create these policies manually in the Supabase dashboard:');
    console.log('1. Go to Storage > Policies in your Supabase dashboard');
    console.log('2. Create the following policies for the storage.objects table:');
    RLS_POLICIES.forEach((policy, index) => {
      console.log(`\n${index + 1}. ${policy.name}:`);
      console.log(policy.sql.trim());
    });
  }
  
  return allSuccess;
}

/**
 * Verify bucket setup
 */
async function verifySetup(): Promise<boolean> {
  try {
    console.log('🔍 Verifying bucket setup...');
    
    // Check bucket exists
    const bucketExists = await checkBucketExists();
    if (!bucketExists) {
      console.error('❌ Bucket does not exist');
      return false;
    }
    
    // Get bucket details
    const { data: bucket, error } = await supabase.storage.getBucket(BUCKET_CONFIG.name);
    if (error) {
      console.error('❌ Error getting bucket details:', error);
      return false;
    }
    
    console.log('✅ Bucket verification completed');
    console.log('📋 Bucket details:');
    console.log(`  • Name: ${bucket.name}`);
    console.log(`  • Public: ${bucket.public}`);
    console.log(`  • Created: ${bucket.created_at}`);
    console.log(`  • Updated: ${bucket.updated_at}`);
    
    return true;
  } catch (error) {
    console.error('❌ Error verifying setup:', error);
    return false;
  }
}

/**
 * Display setup summary
 */
function displaySetupSummary() {
  console.log('\n' + '='.repeat(60));
  console.log('📁 DOCUMENT ATTACHMENTS STORAGE SETUP COMPLETE');
  console.log('='.repeat(60));
  console.log('');
  console.log('✅ Storage bucket configured successfully!');
  console.log('');
  console.log('📋 Configuration Details:');
  console.log(`   • Bucket Name: ${BUCKET_CONFIG.name}`);
  console.log(`   • Public Access: ${BUCKET_CONFIG.public ? 'Yes' : 'No (Private)'}`);
  console.log(`   • File Size Limit: ${BUCKET_CONFIG.fileSizeLimit / (1024 * 1024)}MB`);
  console.log(`   • Allowed File Types: ${BUCKET_CONFIG.allowedMimeTypes.length} types`);
  console.log('');
  console.log('🔒 Security Features:');
  console.log('   • Row Level Security (RLS) enabled');
  console.log('   • Organization-level file isolation');
  console.log('   • Authenticated user access only');
  console.log('');
  console.log('🚀 Next Steps:');
  console.log('   1. The document attachment feature is now ready to use');
  console.log('   2. Navigate to /protected/document/attachments in your app');
  console.log('   3. Upload and manage documents securely');
  console.log('');
  console.log('📚 Documentation:');
  console.log('   • Setup Guide: docs/document-attachments-setup.md');
  console.log('   • API Reference: Available in the documentation');
  console.log('');
  console.log('='.repeat(60));
}

/**
 * Main setup function
 */
async function main(): Promise<void> {
  try {
    console.log('🚀 Starting Document Attachments Storage Setup...');
    console.log('');
    
    // Check if bucket already exists
    const bucketExists = await checkBucketExists();
    
    if (bucketExists) {
      console.log(`ℹ️  Bucket '${BUCKET_CONFIG.name}' already exists`);
      console.log('🔧 Updating configuration...');
      
      // Update existing bucket configuration
      const updateSuccess = await updateBucketConfig();
      if (!updateSuccess) {
        console.error('❌ Failed to update bucket configuration');
        process.exit(1);
      }
    } else {
      console.log(`📁 Creating new bucket: ${BUCKET_CONFIG.name}`);
      
      // Create new bucket
      const createSuccess = await createBucket();
      if (!createSuccess) {
        console.error('❌ Failed to create bucket');
        process.exit(1);
      }
    }
    
    // Setup RLS policies
    const rlsSuccess = await setupRLSPolicies();
    if (!rlsSuccess) {
      console.warn('⚠️  Some RLS policies may need manual setup');
    }
    
    // Verify setup
    const verifySuccess = await verifySetup();
    if (!verifySuccess) {
      console.error('❌ Setup verification failed');
      process.exit(1);
    }
    
    // Display success summary
    displaySetupSummary();
    
  } catch (error) {
    console.error('❌ Setup failed:', error);
    process.exit(1);
  }
}

// Run the setup
main();
