# Document Attachments Storage Setup

This directory contains scripts to automatically set up the Supabase storage bucket required for the document attachments feature.

## Overview

The document attachments feature requires a properly configured Supabase storage bucket with:
- Private bucket for security
- Row Level Security (RLS) policies for organization-level isolation
- File type and size restrictions
- Proper permissions for authenticated users

## Usage

### Quick Setup

```bash
# Setup with local environment (.env.local)
./scripts/storage/setup/run.sh

# Setup with cloud environment (.env)
./scripts/storage/setup/run.sh --env=cloud
```

### Manual TypeScript Execution

```bash
# Using local environment
ts-node scripts/storage/setup/setup-bucket.ts

# Using cloud environment  
ts-node scripts/storage/setup/setup-bucket.ts --env=cloud
```

## What the Script Does

1. **Creates Storage Bucket**: Creates the `document-attachments` bucket if it doesn't exist
2. **Configures Security**: Sets the bucket to private (not publicly accessible)
3. **Sets File Restrictions**: Configures allowed file types and size limits (50MB)
4. **Applies RLS Policies**: Creates Row Level Security policies for:
   - Authenticated user uploads
   - Organization-level file access
   - Organization-level file deletion
   - Organization-level file updates

## Configuration Details

### Bucket Configuration
- **Name**: `document-attachments`
- **Public**: `false` (private bucket)
- **File Size Limit**: 50MB
- **Allowed File Types**:
  - Documents: PDF, Word, Excel, PowerPoint, Text, CSV
  - Images: JPEG, PNG, GIF, WebP, SVG
  - Archives: ZIP, RAR, 7Z

### RLS Policies

The script creates the following Row Level Security policies:

1. **Allow authenticated uploads**: Permits authenticated users to upload files
2. **Allow organization file access**: Users can only view files from their organization
3. **Allow organization file deletion**: Users can only delete files from their organization
4. **Allow organization file updates**: Users can only update files from their organization

## Prerequisites

- Node.js and npm installed
- TypeScript (`ts-node`) available
- Supabase project with service role key
- Environment variables configured:
  - `NEXT_PUBLIC_SUPABASE_URL`
  - `SUPABASE_SERVICE_ROLE_KEY`

## Environment Files

The script supports two environment configurations:

- **Local** (default): Uses `.env.local` file
- **Cloud**: Uses `.env` file

## Troubleshooting

### Common Issues

1. **Missing Environment Variables**
   ```
   Missing environment variables:
   - NEXT_PUBLIC_SUPABASE_URL: ✗
   - SUPABASE_SERVICE_ROLE_KEY: ✗
   ```
   **Solution**: Ensure your environment file contains the required Supabase configuration.

2. **RLS Policy Creation Fails**
   ```
   ⚠️  Some RLS policies may need manual setup
   ```
   **Solution**: The script will provide the SQL commands to run manually in the Supabase dashboard.

3. **Bucket Already Exists**
   ```
   ℹ️  Bucket 'document-attachments' already exists
   ```
   **Solution**: This is normal. The script will update the existing bucket configuration.

### Manual Policy Setup

If RLS policies fail to create automatically, you can create them manually:

1. Go to your Supabase dashboard
2. Navigate to **Storage** > **Policies**
3. Create policies for the `storage.objects` table using the SQL provided by the script

## Verification

After running the script, you can verify the setup by:

1. Checking the Supabase dashboard for the `document-attachments` bucket
2. Verifying the bucket is set to private
3. Confirming RLS policies are applied
4. Testing file upload in the application at `/protected/document/attachments/upload`

## Integration

Once the storage is set up, the document attachments feature will be fully functional:

- Navigate to `/protected/document/attachments` to view uploaded documents
- Navigate to `/protected/document/attachments/upload` to upload new documents
- All file operations will respect organization-level security

## Related Documentation

- [Document Attachments Setup Guide](../../../docs/document-attachments-setup.md)
- [Supabase Storage Documentation](https://supabase.com/docs/guides/storage)
- [Row Level Security Guide](https://supabase.com/docs/guides/auth/row-level-security)
