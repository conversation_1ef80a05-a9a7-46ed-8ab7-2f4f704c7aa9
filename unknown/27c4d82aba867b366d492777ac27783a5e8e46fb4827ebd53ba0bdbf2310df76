import { createClient } from "@/lib/supabase/server";
import { Database } from "@/lib/types/database.types";
import {
  ServiceResponse,
  successResponse,
  errorResponse,
} from "@/lib/types/responses/serviceResponse";

// Type definitions from database schema
type EmployeeAvailability = Database["public"]["Tables"]["employee_availability"]["Row"];
type EmployeeAvailabilityInsert = Database["public"]["Tables"]["employee_availability"]["Insert"];
type EmployeeAvailabilityUpdate = Database["public"]["Tables"]["employee_availability"]["Update"];
type Employee = Database["public"]["Tables"]["employees"]["Row"];

// Extended types for availability operations
export interface AvailabilitySlot {
  id: string;
  employeeId: string;
  dayOfWeek: number; // 0 = Sunday, 1 = Monday, etc.
  startTime: string;
  endTime: string;
  isAvailable: boolean;
  notes?: string;
}

export interface EmployeeSchedule {
  employeeId: string;
  employeeName: string;
  date: string;
  availabilitySlots: AvailabilitySlot[];
  assignments: Array<{
    appointmentId: string;
    appointmentTitle: string;
    startTime: string;
    endTime: string;
    status: string;
  }>;
  totalHours: number;
  availableHours: number;
  workloadStatus: "light" | "normal" | "busy" | "overloaded";
}

export interface AvailabilityFilters {
  organizationId: string;
  employeeId?: string;
  dateFrom?: string;
  dateTo?: string;
  dayOfWeek?: number;
  isAvailable?: boolean;
}

export interface WorkloadDistribution {
  employeeId: string;
  employeeName: string;
  totalAssignments: number;
  totalHours: number;
  averageHoursPerDay: number;
  workloadPercentage: number;
  status: "underutilized" | "optimal" | "overloaded";
  recommendations: string[];
}

/**
 * EmployeeAvailabilityService - Server-side service for managing employee availability
 * Handles availability tracking, workload distribution, and schedule optimization
 */
export class EmployeeAvailabilityService {
  private async getSupabase() {
    return createClient();
  }

  /**
   * Get employee availability for a specific date range
   */
  async getEmployeeAvailability(
    filters: AvailabilityFilters
  ): Promise<ServiceResponse<EmployeeAvailability[]>> {
    try {
      const supabase = await this.getSupabase();
      let query = supabase
        .from("employee_availability")
        .select("*")
        .eq("organization_id", filters.organizationId);

      // Apply filters
      if (filters.employeeId) {
        query = query.eq("employee_id", filters.employeeId);
      }

      if (filters.dayOfWeek !== undefined) {
        query = query.eq("day_of_week", filters.dayOfWeek);
      }

      if (filters.isAvailable !== undefined) {
        query = query.eq("is_available", filters.isAvailable);
      }

      const { data, error } = await query.order("day_of_week").order("start_time");

      if (error) {
        return errorResponse(error, `Failed to fetch availability: ${error.message}`);
      }

      return successResponse(data || [], "Availability fetched successfully");
    } catch (error) {
      return errorResponse(error, "Availability fetch failed");
    }
  }

  /**
   * Get employee schedule for a specific date
   */
  async getEmployeeSchedule(
    employeeId: string,
    date: string,
    organizationId: string
  ): Promise<ServiceResponse<EmployeeSchedule>> {
    try {
      const supabase = await this.getSupabase();

      // Get employee details
      const { data: employee, error: employeeError } = await supabase
        .from("employees")
        .select("*")
        .eq("id", employeeId)
        .eq("organization_id", organizationId)
        .single();

      if (employeeError || !employee) {
        return errorResponse("Employee not found", "Employee not found");
      }

      // Get day of week for the date
      const dayOfWeek = new Date(date).getDay();

      // Get availability slots for this day
      const { data: availability, error: availabilityError } = await supabase
        .from("employee_availability")
        .select("*")
        .eq("employee_id", employeeId)
        .eq("organization_id", organizationId)
        .eq("day_of_week", dayOfWeek);

      if (availabilityError) {
        return errorResponse(
          availabilityError,
          `Failed to fetch availability: ${availabilityError.message}`
        );
      }

      // Get assignments for this date
      const { data: assignments, error: assignmentError } = await supabase
        .from("appointment_assignments")
        .select(
          `
          *,
          appointments!appointment_assignments_appointment_id_fkey(*)
        `
        )
        .eq("employee_id", employeeId)
        .eq("organization_id", organizationId);

      if (assignmentError) {
        return errorResponse(
          assignmentError,
          `Failed to fetch assignments: ${assignmentError.message}`
        );
      }

      // Filter assignments for the specific date
      const dateAssignments =
        assignments?.filter(
          (assignment: any) => assignment.appointments?.appointment_date === date
        ) || [];

      // Convert availability to slots
      const availabilitySlots: AvailabilitySlot[] = (availability || []).map((slot) => ({
        id: slot.id,
        employeeId: slot.employee_id,
        dayOfWeek: slot.day_of_week,
        startTime: slot.start_time,
        endTime: slot.end_time,
        isAvailable: true, // Default to available since this is an availability slot
        notes: undefined, // Database doesn't have notes field
      }));

      // Calculate total and available hours
      let totalHours = 0;
      let assignedHours = 0;

      for (const slot of availabilitySlots) {
        if (slot.isAvailable) {
          const start = new Date(`2000-01-01T${slot.startTime}`);
          const end = new Date(`2000-01-01T${slot.endTime}`);
          totalHours += (end.getTime() - start.getTime()) / (1000 * 60 * 60);
        }
      }

      for (const assignment of dateAssignments) {
        const appointment = assignment.appointments;
        if (appointment) {
          const start = new Date(`2000-01-01T${appointment.start_time}`);
          const end = new Date(`2000-01-01T${appointment.end_time}`);
          assignedHours += (end.getTime() - start.getTime()) / (1000 * 60 * 60);
        }
      }

      const availableHours = totalHours - assignedHours;

      // Determine workload status
      let workloadStatus: "light" | "normal" | "busy" | "overloaded" = "light";
      const utilizationRate = totalHours > 0 ? assignedHours / totalHours : 0;

      if (utilizationRate >= 0.9) {
        workloadStatus = "overloaded";
      } else if (utilizationRate >= 0.7) {
        workloadStatus = "busy";
      } else if (utilizationRate >= 0.4) {
        workloadStatus = "normal";
      }

      const schedule: EmployeeSchedule = {
        employeeId,
        employeeName: `${employee.first_name} ${employee.last_name}`,
        date,
        availabilitySlots,
        assignments: dateAssignments.map((assignment: any) => ({
          appointmentId: assignment.appointments.id,
          appointmentTitle: assignment.appointments.title || "Appointment",
          startTime: assignment.appointments.start_time,
          endTime: assignment.appointments.end_time,
          status: assignment.appointments.status,
        })),
        totalHours,
        availableHours,
        workloadStatus,
      };

      return successResponse(schedule, "Schedule fetched successfully");
    } catch (error) {
      return errorResponse(error, "Schedule fetch failed");
    }
  }

  /**
   * Update employee availability
   */
  async updateEmployeeAvailability(
    availabilityId: string,
    updates: EmployeeAvailabilityUpdate,
    organizationId: string
  ): Promise<ServiceResponse<EmployeeAvailability>> {
    try {
      const supabase = await this.getSupabase();
      const { data, error } = await supabase
        .from("employee_availability")
        .update(updates)
        .eq("id", availabilityId)
        .eq("organization_id", organizationId)
        .select()
        .single();

      if (error) {
        return errorResponse(error, `Failed to update availability: ${error.message}`);
      }

      return successResponse(data, "Availability updated successfully");
    } catch (error) {
      return errorResponse(error, "Availability update failed");
    }
  }

  /**
   * Create new availability slot
   */
  async createAvailabilitySlot(
    availabilityData: EmployeeAvailabilityInsert
  ): Promise<ServiceResponse<EmployeeAvailability>> {
    try {
      const supabase = await this.getSupabase();
      const { data, error } = await supabase
        .from("employee_availability")
        .insert(availabilityData)
        .select()
        .single();

      if (error) {
        return errorResponse(error, `Failed to create availability: ${error.message}`);
      }

      return successResponse(data, "Availability created successfully");
    } catch (error) {
      return errorResponse(error, "Availability creation failed");
    }
  }

  /**
   * Get workload distribution across employees
   */
  async getWorkloadDistribution(
    organizationId: string,
    dateFrom: string,
    dateTo: string
  ): Promise<ServiceResponse<WorkloadDistribution[]>> {
    try {
      const supabase = await this.getSupabase();

      // Get all active employees
      const { data: employees, error: employeeError } = await supabase
        .from("employees")
        .select("*")
        .eq("organization_id", organizationId)
        .eq("is_active", true);

      if (employeeError) {
        return errorResponse(employeeError, `Failed to fetch employees: ${employeeError.message}`);
      }

      if (!employees) {
        return successResponse([], "No employees found");
      }

      const workloadDistribution: WorkloadDistribution[] = [];

      for (const employee of employees) {
        // Get assignments for the date range
        const { data: assignments, error: assignmentError } = await supabase
          .from("appointment_assignments")
          .select(
            `
            *,
            appointments!appointment_assignments_appointment_id_fkey(*)
          `
          )
          .eq("employee_id", employee.id)
          .eq("organization_id", organizationId);

        if (assignmentError) {
          continue; // Skip this employee if we can't get their assignments
        }

        // Filter assignments by date range
        const filteredAssignments =
          assignments?.filter((assignment: any) => {
            const appointmentDate = assignment.appointments?.appointment_date;
            return appointmentDate && appointmentDate >= dateFrom && appointmentDate <= dateTo;
          }) || [];

        // Calculate total hours
        let totalHours = 0;
        for (const assignment of filteredAssignments) {
          const appointment = assignment.appointments;
          if (appointment) {
            const start = new Date(`2000-01-01T${appointment.start_time}`);
            const end = new Date(`2000-01-01T${appointment.end_time}`);
            totalHours += (end.getTime() - start.getTime()) / (1000 * 60 * 60);
          }
        }

        // Calculate date range in days
        const startDate = new Date(dateFrom);
        const endDate = new Date(dateTo);
        const daysDiff =
          Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;

        const averageHoursPerDay = daysDiff > 0 ? totalHours / daysDiff : 0;

        // Determine status and recommendations
        let status: "underutilized" | "optimal" | "overloaded" = "optimal";
        const recommendations: string[] = [];

        if (averageHoursPerDay < 4) {
          status = "underutilized";
          recommendations.push("Consider assigning more appointments");
          recommendations.push("Review availability settings");
        } else if (averageHoursPerDay > 8) {
          status = "overloaded";
          recommendations.push("Consider redistributing some appointments");
          recommendations.push("Review workload limits");
        }

        // Calculate workload percentage (assuming 8 hours as 100%)
        const workloadPercentage = Math.min((averageHoursPerDay / 8) * 100, 100);

        workloadDistribution.push({
          employeeId: employee.id,
          employeeName: `${employee.first_name} ${employee.last_name}`,
          totalAssignments: filteredAssignments.length,
          totalHours,
          averageHoursPerDay,
          workloadPercentage,
          status,
          recommendations,
        });
      }

      return successResponse(workloadDistribution, "Workload distribution calculated successfully");
    } catch (error) {
      return errorResponse(error, "Workload distribution failed");
    }
  }

  /**
   * Find optimal employee for assignment based on availability and workload
   */
  async findOptimalEmployee(
    appointmentDate: string,
    startTime: string,
    endTime: string,
    organizationId: string,
    requiredSkills: string[] = []
  ): Promise<ServiceResponse<Employee[]>> {
    try {
      const supabase = await this.getSupabase();

      // Get day of week
      const dayOfWeek = new Date(appointmentDate).getDay();

      // Get employees with availability for this day and time
      const { data: availability, error: availabilityError } = await supabase
        .from("employee_availability")
        .select(
          `
          *,
          employee:employees(*)
        `
        )
        .eq("organization_id", organizationId)
        .eq("day_of_week", dayOfWeek)
        .eq("is_available", true)
        .lte("start_time", startTime)
        .gte("end_time", endTime);

      if (availabilityError) {
        return errorResponse(
          availabilityError,
          `Failed to fetch availability: ${availabilityError.message}`
        );
      }

      if (!availability || availability.length === 0) {
        return successResponse([], "No available employees found");
      }

      // Get unique employees
      const availableEmployees = availability
        .map((slot: any) => slot.employee)
        .filter(
          (employee: any, index: number, self: any[]) =>
            employee && self.findIndex((e: any) => e.id === employee.id) === index
        );

      // TODO: Filter by required skills when skill system is implemented
      // TODO: Sort by workload and other optimization criteria

      return successResponse(availableEmployees, "Optimal employees found successfully");
    } catch (error) {
      return errorResponse(error, "Optimal employee search failed");
    }
  }

  /**
   * Check if employee is available for a specific time slot
   */
  async isEmployeeAvailable(
    employeeId: string,
    appointmentDate: string,
    startTime: string,
    endTime: string,
    organizationId: string
  ): Promise<ServiceResponse<boolean>> {
    try {
      const supabase = await this.getSupabase();

      const dayOfWeek = new Date(appointmentDate).getDay();

      // Check availability slots
      const { data: availability, error: availabilityError } = await supabase
        .from("employee_availability")
        .select("*")
        .eq("employee_id", employeeId)
        .eq("organization_id", organizationId)
        .eq("day_of_week", dayOfWeek)
        .eq("is_available", true)
        .lte("start_time", startTime)
        .gte("end_time", endTime);

      if (availabilityError) {
        return errorResponse(
          availabilityError,
          `Failed to check availability: ${availabilityError.message}`
        );
      }

      const hasAvailability = availability && availability.length > 0;

      if (!hasAvailability) {
        return successResponse(false, "Employee not available");
      }

      // Check for conflicting assignments
      const { data: conflicts, error: conflictError } = await supabase
        .from("appointment_assignments")
        .select(
          `
          *,
          appointments!appointment_assignments_appointment_id_fkey(*)
        `
        )
        .eq("employee_id", employeeId)
        .eq("organization_id", organizationId);

      if (conflictError) {
        return errorResponse(conflictError, `Failed to check conflicts: ${conflictError.message}`);
      }

      // Check for time conflicts
      const hasConflicts = conflicts?.some((assignment: any) => {
        const appointment = assignment.appointments;
        if (!appointment || appointment.appointment_date !== appointmentDate) {
          return false;
        }

        const appointmentStart = new Date(`${appointmentDate}T${startTime}`);
        const appointmentEnd = new Date(`${appointmentDate}T${endTime}`);
        const existingStart = new Date(`${appointment.appointment_date}T${appointment.start_time}`);
        const existingEnd = new Date(`${appointment.appointment_date}T${appointment.end_time}`);

        return (
          (appointmentStart < existingEnd && appointmentEnd > existingStart) ||
          (existingStart < appointmentEnd && existingEnd > appointmentStart)
        );
      });

      return successResponse(!hasConflicts, "Availability check completed");
    } catch (error) {
      return errorResponse(error, "Availability check failed");
    }
  }
}
