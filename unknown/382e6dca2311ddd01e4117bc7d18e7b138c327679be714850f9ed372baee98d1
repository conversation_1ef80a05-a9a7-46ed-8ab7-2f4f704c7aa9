#!/usr/bin/env bash
#
# run.sh
#
# This script sets up the storage bucket for document attachments.
#
# Usage:
#   ./scripts/storage/setup/run.sh [--env=local|cloud]
#
# Options:
#   --env=local    Use local environment (.env.local) - default
#   --env=cloud    Use cloud environment (.env)
#

# Exit on error, undefined variables, and propagate pipe errors
set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
  echo -e "${BLUE}[INFO]${NC} $1" >&2
}

log_success() {
  echo -e "${GREEN}[SUCCESS]${NC} $1" >&2
}

log_warning() {
  echo -e "${YELLOW}[WARNING]${NC} $1" >&2
}

log_error() {
  echo -e "${RED}[ERROR]${NC} $1" >&2
}

# Function to setup storage bucket
setup_storage_bucket() {
  local env_arg="$1"
  log_info "Setting up storage bucket with environment: $env_arg..."

  if ts-node --compiler-options '{"module":"CommonJS"}' ./scripts/storage/setup/setup-bucket.ts --env="$env_arg"; then
    log_success "Storage bucket setup completed successfully"
    return 0
  else
    log_error "Failed to setup storage bucket"
    return 1
  fi
}

# Function to display setup summary
display_setup_summary() {
  log_info "Document Attachments Storage Setup Complete!"
  echo ""
  echo "┌─────────────────────────────────────────────────────────────┐"
  echo "│                    SETUP SUMMARY                           │"
  echo "├─────────────────────────────────────────────────────────────┤"
  echo "│ ✅ Storage bucket 'document-attachments' configured         │"
  echo "│ ✅ Row Level Security (RLS) policies applied               │"
  echo "│ ✅ File upload restrictions configured                     │"
  echo "│ ✅ Organization-level file isolation enabled               │"
  echo "└─────────────────────────────────────────────────────────────┘"
  echo ""
  log_info "Next steps:"
  log_info "1. Run 'npm run dev' to start the application"
  log_info "2. Navigate to /protected/document/attachments"
  log_info "3. Upload and manage documents securely"
  echo ""
  log_info "Documentation:"
  log_info "• Setup Guide: docs/document-attachments-setup.md"
  log_info "• Feature Documentation: Available in the app"
  echo ""
}

# Parse command line arguments
parse_args() {
  local env_arg="local"  # Default to local environment

  for arg in "$@"; do
    case $arg in
      --env=*)
        env_arg="${arg#*=}"
        if [[ "$env_arg" != "local" && "$env_arg" != "cloud" ]]; then
          log_error "Invalid environment: $env_arg. Must be 'local' or 'cloud'."
          exit 1
        fi
        ;;
      --help|-h)
        echo "Usage: $0 [--env=local|cloud]"
        echo ""
        echo "Options:"
        echo "  --env=local    Use local environment (.env.local) - default"
        echo "  --env=cloud    Use cloud environment (.env)"
        echo "  --help, -h     Show this help message"
        exit 0
        ;;
    esac
  done

  echo "$env_arg"
}

# Check for help before main execution
for arg in "$@"; do
  case $arg in
    --help|-h)
      echo "Usage: $0 [--env=local|cloud]"
      echo ""
      echo "Options:"
      echo "  --env=local    Use local environment (.env.local) - default"
      echo "  --env=cloud    Use cloud environment (.env)"
      echo "  --help, -h     Show this help message"
      exit 0
      ;;
  esac
done

# Main function
main() {
  log_info "Starting Document Attachments Storage Setup..."

  # Parse command line arguments
  local env_arg
  env_arg=$(parse_args "$@")
  log_info "Using environment: $env_arg"

  # Setup storage bucket
  if ! setup_storage_bucket "$env_arg"; then
    log_error "Storage setup failed"
    return 1
  fi

  log_success "Storage setup completed successfully!"
  log_info "Document attachments feature is now ready to use"

  # Display setup summary
  display_setup_summary

  return 0
}

# Run main function with all arguments
main "$@"
