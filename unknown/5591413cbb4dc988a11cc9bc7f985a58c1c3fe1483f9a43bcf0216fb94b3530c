import { createClient } from "@/lib/supabase/server";
import { Database } from "@/lib/types/database.types";
import {
  ServiceResponse,
  successResponse,
  errorResponse,
} from "@/lib/types/responses/serviceResponse";

// Type definitions from database schema
type AppointmentAssignment = Database["public"]["Tables"]["appointment_assignments"]["Row"];
type AppointmentAssignmentInsert =
  Database["public"]["Tables"]["appointment_assignments"]["Insert"];
type AppointmentAssignmentUpdate =
  Database["public"]["Tables"]["appointment_assignments"]["Update"];
type Appointment = Database["public"]["Tables"]["appointments"]["Row"];
type Employee = Database["public"]["Tables"]["employees"]["Row"];

// Extended types for assignment operations
export interface AssignmentWithDetails extends AppointmentAssignment {
  appointments?: Appointment;
  employees?: Employee;
}

export interface CreateAssignmentRequest {
  appointmentId: string;
  employeeId: string;
  assignmentRole?: string;
  organizationId: string;
}

export interface AssignmentFilters {
  organizationId: string;
  employeeId?: string;
  appointmentId?: string;
  assignmentRole?: string;
  dateFrom?: string;
  dateTo?: string;
}

export interface AssignmentHistory {
  id: string;
  action: string;
  assignmentId: string;
  employeeId: string;
  appointmentId: string;
  previousValues?: Record<string, any>;
  newValues?: Record<string, any>;
  performedBy: string;
  performedAt: string;
  notes?: string;
}

/**
 * AssignmentService - Server-side service for managing employee assignments
 * Follows server-first paradigm with no client state management
 */
export class AssignmentService {
  private async getSupabase() {
    return createClient();
  }

  /**
   * Assign an employee to an appointment
   */
  async assignEmployee(
    request: CreateAssignmentRequest
  ): Promise<ServiceResponse<AppointmentAssignment>> {
    try {
      const supabase = await this.getSupabase();

      // Check if assignment already exists
      const { data: existingAssignment } = await supabase
        .from("appointment_assignments")
        .select("*")
        .eq("appointment_id", request.appointmentId)
        .eq("employee_id", request.employeeId)
        .eq("assignment_role", request.assignmentRole || "primary")
        .single();

      if (existingAssignment) {
        return errorResponse(
          "Employee is already assigned to this appointment with the same role",
          "Assignment already exists"
        );
      }

      // Create the assignment
      const assignmentData: AppointmentAssignmentInsert = {
        appointment_id: request.appointmentId,
        employee_id: request.employeeId,
        assignment_role: request.assignmentRole || "primary",
        organization_id: request.organizationId,
      };

      const { data, error } = await supabase
        .from("appointment_assignments")
        .insert(assignmentData)
        .select()
        .single();

      if (error) {
        return errorResponse(error, `Failed to create assignment: ${error.message}`);
      }

      return successResponse(data, "Assignment created successfully");
    } catch (error) {
      return errorResponse(error, "Assignment creation failed");
    }
  }

  /**
   * Get assignments with optional filtering
   */
  async getAssignments(
    filters: AssignmentFilters
  ): Promise<ServiceResponse<AssignmentWithDetails[]>> {
    try {
      const supabase = await this.getSupabase();
      let query = supabase
        .from("appointment_assignments")
        .select(
          `
          *,
          appointments!appointment_assignments_appointment_id_fkey(*),
          employees!appointment_assignments_employee_id_fkey(*)
        `
        )
        .eq("organization_id", filters.organizationId);

      // Apply filters
      if (filters.employeeId) {
        query = query.eq("employee_id", filters.employeeId);
      }

      if (filters.appointmentId) {
        query = query.eq("appointment_id", filters.appointmentId);
      }

      if (filters.assignmentRole) {
        query = query.eq("assignment_role", filters.assignmentRole);
      }

      // Date filtering through appointment date
      if (filters.dateFrom || filters.dateTo) {
        // Note: This requires a more complex query with joins
        // For now, we'll fetch all and filter in memory
        // In production, consider using a database view or stored procedure
      }

      const { data, error } = await query.order("created_at", { ascending: false });

      if (error) {
        return errorResponse(error, `Failed to fetch assignments: ${error.message}`);
      }

      return successResponse(data || [], "Assignments fetched successfully");
    } catch (error) {
      return errorResponse(error, "Assignment fetch failed");
    }
  }

  /**
   * Get a single assignment by ID
   */
  async getAssignmentById(
    assignmentId: string,
    organizationId: string
  ): Promise<ServiceResponse<AssignmentWithDetails>> {
    try {
      const supabase = await this.getSupabase();
      const { data, error } = await supabase
        .from("appointment_assignments")
        .select(
          `
          *,
          appointments!appointment_assignments_appointment_id_fkey(*),
          employees!appointment_assignments_employee_id_fkey(*)
        `
        )
        .eq("id", assignmentId)
        .eq("organization_id", organizationId)
        .single();

      if (error) {
        return errorResponse(error, `Failed to fetch assignment: ${error.message}`);
      }

      return successResponse(data, "Assignment fetched successfully");
    } catch (error) {
      return errorResponse(error, "Assignment fetch failed");
    }
  }

  /**
   * Update an assignment
   */
  async updateAssignment(
    assignmentId: string,
    updates: AppointmentAssignmentUpdate,
    organizationId: string
  ): Promise<ServiceResponse<AppointmentAssignment>> {
    try {
      const supabase = await this.getSupabase();
      const { data, error } = await supabase
        .from("appointment_assignments")
        .update(updates)
        .eq("id", assignmentId)
        .eq("organization_id", organizationId)
        .select()
        .single();

      if (error) {
        return errorResponse(error, `Failed to update assignment: ${error.message}`);
      }

      return successResponse(data, "Assignment updated successfully");
    } catch (error) {
      return errorResponse(error, "Assignment update failed");
    }
  }

  /**
   * Remove an assignment
   */
  async removeAssignment(
    assignmentId: string,
    organizationId: string
  ): Promise<ServiceResponse<void>> {
    try {
      const supabase = await this.getSupabase();
      const { error } = await supabase
        .from("appointment_assignments")
        .delete()
        .eq("id", assignmentId)
        .eq("organization_id", organizationId);

      if (error) {
        return errorResponse(error, `Failed to remove assignment: ${error.message}`);
      }

      return successResponse(undefined, "Assignment removed successfully");
    } catch (error) {
      return errorResponse(error, "Assignment removal failed");
    }
  }

  /**
   * Get assignments for a specific employee
   */
  async getEmployeeAssignments(
    employeeId: string,
    organizationId: string,
    dateFrom?: string,
    dateTo?: string
  ): Promise<ServiceResponse<AssignmentWithDetails[]>> {
    return this.getAssignments({
      organizationId,
      employeeId,
      dateFrom,
      dateTo,
    });
  }

  /**
   * Get assignments for a specific appointment
   */
  async getAppointmentAssignments(
    appointmentId: string,
    organizationId: string
  ): Promise<ServiceResponse<AssignmentWithDetails[]>> {
    return this.getAssignments({
      organizationId,
      appointmentId,
    });
  }

  /**
   * Get assignment history (placeholder - would need a separate audit table)
   */
  async getAssignmentHistory(
    assignmentId: string,
    organizationId: string
  ): Promise<ServiceResponse<AssignmentHistory[]>> {
    try {
      // This is a placeholder implementation
      // In a real system, you would have an audit/history table
      const mockHistory: AssignmentHistory[] = [
        {
          id: "1",
          action: "created",
          assignmentId,
          employeeId: "employee-1",
          appointmentId: "appointment-1",
          performedBy: "user-1",
          performedAt: new Date().toISOString(),
          notes: "Assignment created",
        },
      ];

      return successResponse(mockHistory, "Assignment history fetched successfully");
    } catch (error) {
      return errorResponse(error, "Assignment history fetch failed");
    }
  }

  /**
   * Bulk assign employees to an appointment
   */
  async bulkAssignEmployees(
    appointmentId: string,
    employeeAssignments: Array<{
      employeeId: string;
      assignmentRole: string;
    }>,
    organizationId: string
  ): Promise<ServiceResponse<AppointmentAssignment[]>> {
    try {
      const supabase = await this.getSupabase();
      const assignmentData: AppointmentAssignmentInsert[] = employeeAssignments.map(
        (assignment) => ({
          appointment_id: appointmentId,
          employee_id: assignment.employeeId,
          assignment_role: assignment.assignmentRole,
          organization_id: organizationId,
        })
      );

      const { data, error } = await supabase
        .from("appointment_assignments")
        .insert(assignmentData)
        .select();

      if (error) {
        return errorResponse(error, `Failed to create bulk assignments: ${error.message}`);
      }

      return successResponse(data || [], "Bulk assignments created successfully");
    } catch (error) {
      return errorResponse(error, "Bulk assignment failed");
    }
  }
}
